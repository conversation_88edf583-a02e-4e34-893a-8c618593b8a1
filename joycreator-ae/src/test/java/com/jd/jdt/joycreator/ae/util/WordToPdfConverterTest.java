package com.jd.jdt.joycreator.ae.util;

import org.junit.jupiter.api.Test;
import java.io.File;
import static org.junit.jupiter.api.Assertions.*;

public class WordToPdfConverterTest {

    private static final String INPUT_DOCX_PATH = "/Users/<USER>/IdeaProjects/JoyCreator2/joycreator-ae/src/main/resources/scripts/【样例】技术服务协议模板（jd为销售方通用版）.docx";
    private static final String OUTPUT_PDF_PATH = "target/test-output/技术服务协议模板.pdf";

    @Test
    public void testConvertToPdf() throws Exception {
        // 确保输入文件存在
        File inputFile = new File(INPUT_DOCX_PATH);
        assertTrue(inputFile.exists(), "输入DOCX文件不存在");

        // 创建输出目录
        File outputDir = new File(OUTPUT_PDF_PATH).getParentFile();
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        // 执行转换并捕获异常
        try {
            WordToPdfConverter.convertToPdf(INPUT_DOCX_PATH, OUTPUT_PDF_PATH);
        } catch (Exception e) {
            e.printStackTrace();
            fail("PDF转换失败: " + e.getMessage());
        }

        // 验证输出文件存在且不为空
        File outputFile = new File(OUTPUT_PDF_PATH);
        assertTrue(outputFile.exists(), "PDF输出文件未生成");
        assertTrue(outputFile.length() > 0, "PDF文件内容为空");

        // 清理测试文件
        outputFile.deleteOnExit();
    }
}