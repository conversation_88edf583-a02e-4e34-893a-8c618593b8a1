{"description": "", "draft": false, "editable": true, "edition": "v2", "isTree": false, "modelFields": [{"businessType": "number", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "主键id", "fieldFormat": "", "fieldLength": 64, "fieldName": "id", "fieldShow": false, "fieldText": "主键id", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "pointLength": 0, "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "string", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "名称", "fieldFormat": "", "fieldLength": 100, "fieldName": "name", "fieldShow": true, "fieldText": "名称", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": true, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "user", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "归属人（erp）", "fieldFormat": "", "fieldLength": 64, "fieldName": "owner_id", "fieldShow": false, "fieldText": "归属人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "dateTime", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "创建时间", "fieldFormat": "", "fieldLength": 0, "fieldName": "create_time", "fieldShow": false, "fieldText": "创建时间", "fieldTrack": false, "fieldType": "date", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "dateTime", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "更新时间", "fieldFormat": "", "fieldLength": 0, "fieldName": "update_time", "fieldShow": false, "fieldText": "更新时间", "fieldTrack": false, "fieldType": "date", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "user", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "创建人", "fieldFormat": "", "fieldLength": 100, "fieldName": "created_user", "fieldShow": false, "fieldText": "创建人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "user", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "修改人", "fieldFormat": "", "fieldLength": 100, "fieldName": "modified_user", "fieldShow": false, "fieldText": "修改人", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "checkBox", "defaultField": true, "defaultValueType": 0, "entityField": false, "fieldComment": "是否有效(1:有效,0:无效)", "fieldFormat": "", "fieldLength": 1, "fieldName": "yn", "fieldShow": false, "fieldText": "是否有效", "fieldTrack": false, "fieldType": "boolean", "fileMultiple": "0", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": true, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "string", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 128, "fieldName": "prompt_word_code", "fieldShow": true, "fieldText": "提示词编码", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "text", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 30000, "fieldName": "prompt_word", "fieldShow": true, "fieldText": "提示词", "fieldTrack": false, "fieldType": "text", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "text", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 1000, "fieldName": "description", "fieldShow": true, "fieldText": "描述/说明", "fieldTrack": false, "fieldType": "text", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"actualDefaultValue": "", "businessType": "string", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "{\"showType\":\"select\"}", "fieldLength": 256, "fieldName": "model", "fieldShow": true, "fieldText": "模型", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "fileSuffix": "[]", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": true, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataJson": "[]", "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}, {"businessType": "number", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 10, "fieldName": "temperature", "fieldShow": true, "fieldText": "采样温度", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "fileSuffix": "[]", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "pointLength": 2, "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"businessType": "number", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 10, "fieldName": "top_k", "fieldShow": true, "fieldText": "核取样", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "fileSuffix": "[]", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "pointLength": 2, "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"businessType": "number", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 10, "fieldName": "max_tokens", "fieldShow": true, "fieldText": "输出最大token数", "fieldTrack": false, "fieldType": "number", "fileMultiple": "0", "fileSuffix": "[]", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "pointLength": 2, "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": true}, {"actualDefaultValue": "", "businessType": "string", "defaultField": false, "defaultValue": "", "defaultValueType": 0, "entityField": false, "fieldComment": "", "fieldFormat": "", "fieldLength": 128, "fieldName": "prompt_source", "fieldShow": true, "fieldText": "提示词来源", "fieldTrack": false, "fieldType": "string", "fileMultiple": "0", "help": "", "inDbRule": "", "jumpOrNot": true, "lowerSensitive": false, "maxFileCount": "1", "must": false, "outDbRule": "", "relatedOriginModel": "0", "searchShow": false, "selectDataSource": 0, "syncFieldData": false, "tableFieldName": "", "thousandsSeparator": false}], "modelName": "prompt_words_config", "modelText": "提示词管理", "modelType": 0, "tableName": "", "vn": 0, "wfOpen": false}