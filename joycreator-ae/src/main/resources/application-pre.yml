server:
  port: 8091
logging:
  config: classpath:log/log4j2.yaml
app4s:
  application:
    id: '2943'
  datahub:
    open: false
    env: dev
  zk:
    url: testpubli.zk.jddb.com:2181
    namespace: publictest
    userName: admin
    password: admin888
  useCodeTemplate: false
  permissionInterceptor: com.jd.jdt.app4s.permission.embed.interceptor.JoyBuilderPermissionEmbedInterceptor
  permissionType: IPMS
  permissionAutoSave: user
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driverClassName: com.p6spy.engine.spy.P6SpyDriver
      url: ************************************************************************************************************************************************************************************************************************************
      username: y_joycreator_pre
      password: rQdR/owTju3D5IVuxeAiVQ==
workflow:
  alias: pro-operateFlowApi:0.0.1

rpc:
  llm:
    url: 'http://gpt-proxy.jd.com'
    apiKey: 'aa051d32-db0b-4294-98a0-b02040240d48'
    jdcloudPin: 'jcloud_doNGoJh'
  office:
    url: http://joyoffice.jd.com
  aspose:
    url: https://convert.jd.com

oss:
  accessKey: RE3LMZKZ4PD833HFTLYKD2XP3LH46GO0
  secretKey: 7KQWTOYZBJZ9WWNIK9J8PBCQQXR4KNYK
  endpoint: https://s3.jdpay.com
  intranetEndpoint: http://s3-internal-office-tech-north-1.jdfmgt.com
  bucket: joycreator-pre
  protocol: http

r2m:
  appName: xinghui_r2m
  3cConnectionStr: r2m3c.jdfin.local
  3cToken: a4bc676ca4
  password: UiZ5p7e1MfgMqVX3KnYxS5JcV+Y376kCr2iQ4OsNmYU=

# IP白名单配置，多个IP用逗号分隔
ip:
  whitelist: ************