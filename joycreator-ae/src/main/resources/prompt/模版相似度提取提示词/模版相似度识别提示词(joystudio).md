## 任务目标

根据用户输入的语义内容，从给定的文档模版数据中筛选出最匹配的1个模版，并返回其ID及反馈信息。

**匹配门槛规则：**
- **合同类模版**：只要命中任意一个标签就算基础匹配成功，无需达到90%相似度门槛
- **非合同类模版**：严格要求相似度不低于90%才算匹配成功
- 若无任何模版匹配（合同类：无标签命中；非合同类：相似度均低于90%），则只返回`taskFeedback`字段（不输出空对象 `{}`）
- 返回0-1个模版，选择匹配度最高的那个
- `taskFeedback`字段为必输出，无论是否找到模版

---

## 匹配逻辑规则

### 1. 合同类型匹配逻辑（tag为"合同类"）

**优先级排序：**
1. **立场匹配（最高优先级）**：优先匹配合同立场标签
2. **标签列表匹配**：匹配立场后边的标签列表，包括但不限于：
   - 行业/领域标签（如：互联网、金融、制造业等）
   - 交易特征标签（如：买卖、租赁、服务等）
   - 业务特征标签（如：技术服务、货物运输、汽运服务、物流、费用结算等）
   - 自由补齐标签（其他相关关键词）

**新的匹配规则：**
- **基础匹配门槛**：只要命中任意一个标签（包括立场标签或其他标签）就算基础匹配成功
- **权重计算机制**：
  - 立场匹配权重最高，立场相符的模版优先考虑
  - 命中标签数量越多，权重越大，最终得分越高
  - 不区分标签顺序，所有标签平等参与匹配
- **选择规则**：在所有有标签命中的模版中，选择综合得分最高的一个
- **无匹配处理**：如果所有合同类模版都没有任何标签命中，则不返回id字段，只返回taskFeedback

**立场判定逻辑（参考模版类型概述自动填充提示词7.md）：**
- **具体主体**：包含真实公司名称和组织形式的主体（如：北京快手科技有限公司、京东科技信息技术有限公司）
- **模糊主体**：占位符或模板表述（如：【公司名称】、【客户】、【甲方】、【乙方】、___公司等）
- **立场规则**：
  - 具体主体 + 具体主体 → 无立场
  - 模糊主体 + 模糊主体 → 无立场  
  - 具体主体 + 模糊主体 → 具体主体立场
  - 模糊主体 + 具体主体 → 具体主体立场

### 2. 非合同类型匹配逻辑

**匹配步骤：**
1. **文档类型匹配**：根据用户意图识别目标文档类型（tag字段）
2. **描述内容匹配**：在确定文档类型后，根据description字段进行语义相似度匹配
3. **综合评分**：结合文档类型匹配度和描述相似度计算最终得分

**支持的文档类型包括：**
- 文书类、PRD类、制度类、文案类、报告类、方案类、计划类、总结类、流程类、表单类、指南类、协议类、备忘录类、新闻稿类、演讲稿类、论文类、演示文稿类等

---

## 输入格式

- **用户输入语义**（字符串）：  
  用户语意为：`{{input}}`
- **模版名称**（字符串）：  
  name为：`{{input}}`
- **模版数据**（JSON数组，每个元素为对象）：  
  每个对象包含以下字段：
    - `id`（字符串或数字）：模版唯一标识
    - `name`（字符串）：为每一条模板的名称
    - `tag`（字符串）：文档类型（如"合同类"、"文书类"、"PRD类"等）
    - `description`（字符串）：模版描述
        - **"合同类"**：由若干标签拼接而成，标签间用"、"分隔，**立场标签始终放在最前面**。格式为：`立场标签、行业标签、交易特征标签、业务特征标签1、业务特征标签2、...`。例如：`甲方立场、互联网、技术服务、云计算、软件开发`或`无立场、物流、货物运输、汽运服务、费用结算`
        - **非"合同类"**：为文档主要应用场景或核心内容的概述。例如：`描述了CRM系统的产品需求文档，涵盖项目概述、用户需求、功能需求等`
          
  以下为全量模版数据：
  ```json
  {{input}}
  ```

---

## 匹配算法详细说明

### 合同类匹配算法

```
1. 解析用户意图，提取：
   - 立场偏向关键词（甲方、乙方、买方、卖方等）
   - 业务关键词（技术服务、物流、运输、金融等）
   - 行业关键词（互联网、制造业、金融等）

2. 对每个合同类模版：
   a. 解析description字段，提取立场标签和其他标签
   b. 计算立场匹配得分（匹配=100分，不匹配=0分）
   c. 计算标签匹配得分（每个匹配标签+20分）
   d. 综合得分 = 立场得分 × 0.4 + 标签得分 × 0.6

3. 筛选规则：
   a. 基础筛选：只保留至少命中1个标签的模版（立场标签或其他标签均可）
   b. 权重排序：在通过基础筛选的模版中，按综合得分从高到低排序
   c. 最终选择：选择得分最高的1个模版
   d. 无匹配处理：如果没有任何模版命中标签，则不返回id字段
```

### 非合同类匹配算法

```
1. 识别用户意图中的文档类型关键词
2. 筛选出tag字段匹配的模版
3. 对筛选后的模版进行description语义相似度计算
4. 选择相似度≥90%的模版中相似度最高的1个
```

---

## 正确示例

### 示例1：合同类-有匹配结果

**用户输入：**  
`我需要一份关于技术服务的合同，最好是甲方立场`

**模版数据示例：**
```json
[
  {
    "id": 1,
    "name": "技术服务合同A",
    "tag": "合同类",
    "description": "甲方立场、互联网、技术服务、软件开发、合作"
  },
  {
    "id": 2,
    "name": "技术服务合同B", 
    "tag": "合同类",
    "description": "甲方立场、金融、技术服务、系统集成"
  },
  {
    "id": 3,
    "name": "技术服务合同C",
    "tag": "合同类",
    "description": "乙方立场、互联网、技术服务、云计算"
  },
  {
    "id": 4,
    "name": "物流运输合同",
    "tag": "合同类", 
    "description": "乙方立场、物流、货物运输、汽运服务、费用结算"
  }
]
```

**匹配分析：**
- 模版1：立场匹配(甲方立场) + 标签匹配(技术服务) = 最高分
- 模版2：立场匹配(甲方立场) + 标签匹配(技术服务) = 高分
- 模版3：立场不匹配(乙方立场) + 标签匹配(技术服务) = 中等分（仍然匹配成功）
- 模版4：立场不匹配 + 标签不匹配 = 无匹配（无任何标签命中）
- 选择得分最高的模版1

**输出：**
```json
{
  "id": 1,
  "taskFeedback": "我已经为您找到了一份相关的技术服务合同模版，符合您的甲方立场需求。"
}
```

### 示例2：合同类-单个匹配结果

**用户输入：**  
`需要一个物流运输相关的合同模板`

**匹配分析：**
- 提取关键词：物流、运输
- 只有模版4匹配：物流、货物运输
- 虽然立场不匹配用户偏向，但标签匹配成功（命中2个标签：物流、货物运输）
- 按新规则：只要有标签命中就算匹配成功，无需达到90%门槛

**输出：**
```json
{
  "id": 4,
  "taskFeedback": "我已经为您找到了一份相关的物流运输合同模版。"
}
```

### 示例3：非合同类-有匹配结果

**用户输入：**  
`我需要写一个产品需求文档，关于CRM系统的`

**模版数据示例：**
```json
[
  {
    "id": 5,
    "name": "CRM系统PRD",
    "tag": "PRD类",
    "description": "描述了CRM系统的产品需求文档，涵盖项目概述、用户需求、功能需求等"
  },
  {
    "id": 6,
    "name": "ERP系统PRD",
    "tag": "PRD类", 
    "description": "描述了ERP系统的产品需求文档，包含业务流程、功能模块、技术架构等"
  }
]
```

**匹配分析：**
- 模版5：CRM系统PRD，与用户需求完全匹配，相似度最高

**输出：**
```json
{
  "id": 5,
  "taskFeedback": "我已经为您找到了一份相关的CRM系统产品需求文档模版。"
}
```

### 示例4：合同类-无标签匹配

**用户输入：**  
`我需要一份关于房屋买卖的合同`

**模版数据示例：**
```json
[
  {
    "id": 1,
    "name": "技术服务合同A",
    "tag": "合同类",
    "description": "甲方立场、互联网、技术服务、软件开发、合作"
  },
  {
    "id": 4,
    "name": "物流运输合同",
    "tag": "合同类", 
    "description": "乙方立场、物流、货物运输、汽运服务、费用结算"
  }
]
```

**匹配分析：**
- 提取关键词：房屋、买卖
- 所有合同类模版都没有命中任何相关标签
- 按新规则：无任何标签命中，不返回id字段

**输出：**
```json
{
  "taskFeedback": "很抱歉，未能为您找到合适的房屋买卖合同模版，如有其他需求欢迎随时告知。"
}
```

### 示例5：非合同类-无匹配模版

**用户输入：**  
`我需要一份关于员工考勤的管理制度`

**模版数据：**  
假设没有相关模版或相似度均低于90%

**输出：**
```json
{
  "taskFeedback": "很抱歉，未能为您找到合适的员工考勤管理制度模版，如有其他需求欢迎随时告知。"
}
```

---

## 输出格式

- 若找到相似度≥90%的模版，输出如下对象：
```json
{
  "id": 此处填充相似度最高的模版ID,
  "taskFeedback": "此处根据找到的模版自动生成反馈，格式为：我已经为您找到了一份相关的xxxxx模版。要求：语言简洁且突出重点，但是用词要温柔、客气、有一种被服从回复的语气"
}
```
- 若未找到符合条件的模版，仅输出：
```json
{
  "taskFeedback": "很抱歉，未能为您找到合适的xxxxx模版，如有其他需求欢迎随时告知。"
}
```

---

## 错误示例（禁止出现）

### 错误1：输出空对象

```json
{}
```
> 错误：未找到模版时不能输出空对象，必须输出taskFeedback字段。

### 错误2：输出了注释或解释

```json
{"id": 1, "taskFeedback": "已为您找到相关模版。"} // 这是最相关的合同模版
```
> 错误：禁止输出任何注释或解释。

### 错误3：无关键词匹配或相似度不足仍输出id

```json
{"id": 3, "taskFeedback": "..."}
```
> 错误：若所有模版无关键词匹配或相似度均低于90%，只输出taskFeedback字段。

### 错误4：使用ids数组格式

```json
{"ids": [1], "taskFeedback": "..."}
```
> 错误：应使用单个id格式，不是ids数组格式。

### 错误5：有匹配时不输出id字段

```json
{"taskFeedback": "已为您找到相关模版。"}
```
> 错误：有匹配模版时必须输出id字段，不能省略。

### 错误6：输出id为null或空值

```json
{"id": null, "taskFeedback": "..."}
```
> 错误：无匹配时不要输出id字段，有匹配时id必须为具体的模版ID值。

### 错误7：合同类匹配时忽略立场优先级

**错误场景：** 用户明确要求"甲方立场"，但返回结果中选择了"乙方立场"的模版
> 错误：合同类匹配必须优先考虑立场匹配，立场不符的模版权重应显著降低。

---

## 总结

- **合同类匹配**：
  - 基础门槛：只要命中任意一个标签就算匹配成功，无需90%相似度门槛
  - 权重机制：立场优先（最高权重） + 标签匹配数量（权重递增），不区分标签顺序
  - 选择规则：在所有有标签命中的模版中，选择综合得分最高的1个
  - 无匹配处理：如果所有合同类模版都没有任何标签命中，则不返回id字段
- **非合同类匹配**：先匹配文档类型(tag)，再匹配描述内容(description)，严格要求相似度≥90%
- **输出规范**：无论是否找到模版，taskFeedback字段必输出，未找到时只输出taskFeedback，找到时输出id和taskFeedback
- **格式要求**：使用单个id格式，不是ids数组格式
- **参考逻辑**：合同类description的标签拼接逻辑和立场判定规则参考模版类型概述自动填充提示词7.md
