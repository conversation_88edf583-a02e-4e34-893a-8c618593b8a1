<svg width="124" height="124" viewBox="0 0 124 124" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_di_64_3392)">
<rect x="39" y="21" width="72" height="84" rx="12" fill="url(#paint0_linear_64_3392)"/>
</g>
<g filter="url(#filter1_ddi_64_3392)">
<rect x="69" y="40" width="29" height="5" rx="2.5" fill="url(#paint1_linear_64_3392)"/>
<rect x="69.25" y="40.25" width="28.5" height="4.5" rx="2.25" stroke="url(#paint2_radial_64_3392)" stroke-width="0.5"/>
</g>
<g filter="url(#filter2_ddi_64_3392)">
<rect x="69" y="56" width="29" height="5" rx="2.5" fill="url(#paint3_linear_64_3392)"/>
<rect x="69.25" y="56.25" width="28.5" height="4.5" rx="2.25" stroke="url(#paint4_radial_64_3392)" stroke-width="0.5"/>
</g>
<g filter="url(#filter3_ddi_64_3392)">
<rect x="69" y="73" width="29" height="5" rx="2.5" fill="url(#paint5_linear_64_3392)"/>
<rect x="69.25" y="73.25" width="28.5" height="4.5" rx="2.25" stroke="url(#paint6_radial_64_3392)" stroke-width="0.5"/>
</g>
<g filter="url(#filter4_f_64_3392)">
<path d="M65.5 21H84.9999L89.0739 25.7531C90.3167 27.203 90.9999 29.0497 90.9999 30.9594V105H65.9999L65.5 21Z" fill="url(#paint7_linear_64_3392)" fill-opacity="0.3"/>
</g>
<g filter="url(#filter5_d_64_3392)">
<path d="M6 31.8767C6 23.9143 11.8549 17.1636 19.7373 16.0375L57.7373 10.609C67.3762 9.23197 76 16.7114 76 26.4482V97.5519C76 107.289 67.3762 114.768 57.7373 113.391L19.7373 107.962C11.8549 106.836 6 100.086 6 92.1233V31.8767Z" fill="url(#paint8_linear_64_3392)"/>
<path d="M6 31.8767C6 23.9143 11.8549 17.1636 19.7373 16.0375L57.7373 10.609C67.3762 9.23197 76 16.7114 76 26.4482V97.5519C76 107.289 67.3762 114.768 57.7373 113.391L19.7373 107.962C11.8549 106.836 6 100.086 6 92.1233V31.8767Z" fill="url(#paint9_linear_64_3392)"/>
<path d="M57.8076 11.1035C67.1453 9.76956 75.5 17.0157 75.5 26.4482V97.5518C75.5 106.984 67.1453 114.23 57.8076 112.896L19.8076 107.468C12.1717 106.377 6.5 99.8365 6.5 92.123V31.877C6.5 24.1635 12.1717 17.6232 19.8076 16.5322L57.8076 11.1035Z" stroke="url(#paint10_linear_64_3392)"/>
</g>
<g filter="url(#filter6_f_64_3392)">
<path d="M8 30.0058C8 24.0476 12.3717 18.9918 18.2675 18.1316L60.2675 12.0036C67.5079 10.9472 74 16.5609 74 23.8779V100.122C74 107.439 67.5079 113.053 60.2675 111.996L18.2675 105.868C12.3717 105.008 8 99.9524 8 93.9942V30.0058Z" fill="url(#paint11_linear_64_3392)" fill-opacity="0.7"/>
<path d="M8 30.0058C8 24.0476 12.3717 18.9918 18.2675 18.1316L60.2675 12.0036C67.5079 10.9472 74 16.5609 74 23.8779V100.122C74 107.439 67.5079 113.053 60.2675 111.996L18.2675 105.868C12.3717 105.008 8 99.9524 8 93.9942V30.0058Z" fill="url(#paint12_linear_64_3392)"/>
</g>
<g filter="url(#filter7_dd_64_3392)">
<path d="M54.6766 45.4875C55.2111 43.9929 56.8302 43.1646 58.367 43.6281C59.9039 44.0916 60.795 45.677 60.4139 47.2179L60.3729 47.3664L50.8729 78.8664C50.4988 80.1066 49.3711 80.9667 48.076 80.9992C46.781 81.0317 45.6113 80.229 45.1756 79.009L38.0731 59.1222L31.3416 78.964C30.9214 80.2027 29.7471 81.026 28.4393 80.9992C27.2133 80.974 26.1351 80.2071 25.701 79.0773L25.6229 78.8469L18.1229 53.3469L18.0829 53.1974C17.7127 51.6542 18.6144 50.0752 20.1541 49.6222C21.6941 49.1693 23.3077 50.0087 23.8319 51.507L23.8788 51.6535L28.704 68.0617L35.16 49.0363L35.245 48.8146C35.7117 47.7285 36.7805 47.0097 37.9774 47.0002C39.2538 46.9902 40.3967 47.7893 40.826 48.9914L47.7596 68.4074L54.6288 45.634L54.6766 45.4875Z" fill="url(#paint13_linear_64_3392)"/>
<path d="M55.1523 45.6431C55.5741 44.4836 56.7816 43.8101 57.9824 44.0474L58.2227 44.1069C59.4988 44.4918 60.2406 45.8051 59.9316 47.0845L59.8906 47.2329L50.3945 78.7222C50.0828 79.7557 49.1426 80.4725 48.0635 80.4995L47.8623 80.4966C46.9311 80.4452 46.1045 79.8774 45.7217 79.0269L45.6465 78.8403L38.5439 58.9536C38.4723 58.7535 38.2819 58.621 38.0693 58.6226C37.8832 58.624 37.7156 58.7282 37.6309 58.8892L37.5996 58.9614L30.8682 78.8032C30.518 79.8354 29.5391 80.5219 28.4492 80.4995L28.2598 80.4878C27.3199 80.3971 26.5103 79.7832 26.1699 78.9038L26.0967 78.686L18.6025 53.2056L18.5684 53.0806H18.5693C18.2803 51.8752 18.9227 50.6432 20.0615 50.1831L20.2949 50.1021C21.5738 49.7259 22.9131 50.4196 23.3545 51.6597H23.3555L23.4023 51.8062V51.8052L28.2246 68.2026C28.2861 68.4117 28.4755 68.5574 28.6934 68.562C28.9113 68.5665 29.1077 68.4286 29.1777 68.2222L35.6338 49.1968L35.71 48.9995C36.0771 48.158 36.8793 47.5835 37.7959 47.5083L37.9814 47.5005C38.9786 47.4928 39.878 48.0773 40.2812 48.9761L40.3555 49.1597L47.2891 68.5757C47.3617 68.7789 47.5567 68.9131 47.7725 68.9077C47.988 68.9022 48.1758 68.7586 48.2383 68.5522L55.1045 45.7896L55.1523 45.6431Z" stroke="url(#paint14_linear_64_3392)" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<g filter="url(#filter8_f_64_3392)">
<ellipse cx="61.5" cy="118" rx="53.5" ry="1" fill="#70B5EE" fill-opacity="0.49"/>
</g>
<defs>
<filter id="filter0_di_64_3392" x="39" y="21" width="73" height="88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.269783 0 0 0 0 0.610551 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_3392"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_3392" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.730361 0 0 0 0 0.973036 0 0 0 0 1 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_64_3392"/>
</filter>
<filter id="filter1_ddi_64_3392" x="65" y="40" width="37" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.424683 0 0 0 0 0.480179 0 0 0 0 0.979636 0 0 0 0.33 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_3392"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00024858 0 0 0 0 0.16131 0 0 0 0 0.878764 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_64_3392" result="effect2_dropShadow_64_3392"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_64_3392" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.608988 0 0 0 0 0.908764 0 0 0 0 1 0 0 0 0.71 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_64_3392"/>
</filter>
<filter id="filter2_ddi_64_3392" x="65" y="56" width="37" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.424683 0 0 0 0 0.480179 0 0 0 0 0.979636 0 0 0 0.33 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_3392"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00024858 0 0 0 0 0.16131 0 0 0 0 0.878764 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_64_3392" result="effect2_dropShadow_64_3392"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_64_3392" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.608988 0 0 0 0 0.908764 0 0 0 0 1 0 0 0 0.71 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_64_3392"/>
</filter>
<filter id="filter3_ddi_64_3392" x="65" y="73" width="37" height="13" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.424683 0 0 0 0 0.480179 0 0 0 0 0.979636 0 0 0 0.33 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_3392"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00024858 0 0 0 0 0.16131 0 0 0 0 0.878764 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_64_3392" result="effect2_dropShadow_64_3392"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_64_3392" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.608988 0 0 0 0 0.908764 0 0 0 0 1 0 0 0 0.71 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_64_3392"/>
</filter>
<filter id="filter4_f_64_3392" x="64.5" y="20" width="27.5" height="86" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_64_3392"/>
</filter>
<filter id="filter5_d_64_3392" x="6" y="10.4453" width="75" height="103.109" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.245869 0 0 0 0 0.458145 0 0 0 0 0.953457 0 0 0 0.78 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_3392"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_64_3392" result="shape"/>
</filter>
<filter id="filter6_f_64_3392" x="6" y="9.87549" width="70" height="104.249" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_64_3392"/>
</filter>
<filter id="filter7_dd_64_3392" x="16" y="41.4995" width="50.502" height="45.5005" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.135007 0 0 0 0 0.14032 0 0 0 0 0.523751 0 0 0 0.58 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_64_3392"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.274545 0 0 0 0 0.564727 0 0 0 0 1 0 0 0 0.92 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_64_3392" result="effect2_dropShadow_64_3392"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_64_3392" result="shape"/>
</filter>
<filter id="filter8_f_64_3392" x="4" y="113" width="115" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_64_3392"/>
</filter>
<linearGradient id="paint0_linear_64_3392" x1="101.5" y1="26" x2="62.5" y2="93" gradientUnits="userSpaceOnUse">
<stop stop-color="#E5ECF8"/>
<stop offset="1" stop-color="#DBEAFC"/>
</linearGradient>
<linearGradient id="paint1_linear_64_3392" x1="84" y1="44" x2="96.5" y2="40" gradientUnits="userSpaceOnUse">
<stop stop-color="#79B9F4"/>
<stop offset="1" stop-color="#479FF7"/>
</linearGradient>
<radialGradient id="paint2_radial_64_3392" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(86.5 45) rotate(-101.31) scale(2.54951 7.97028)">
<stop stop-color="#C39BFF" stop-opacity="0.5"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_64_3392" x1="84" y1="60" x2="96.5" y2="56" gradientUnits="userSpaceOnUse">
<stop stop-color="#79B9F4"/>
<stop offset="1" stop-color="#479FF7"/>
</linearGradient>
<radialGradient id="paint4_radial_64_3392" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(86.5 61) rotate(-101.31) scale(2.54951 7.97028)">
<stop stop-color="#C39BFF" stop-opacity="0.5"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint5_linear_64_3392" x1="84" y1="77" x2="96.5" y2="73" gradientUnits="userSpaceOnUse">
<stop stop-color="#79B9F4"/>
<stop offset="1" stop-color="#479FF7"/>
</linearGradient>
<radialGradient id="paint6_radial_64_3392" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(86.5 78) rotate(-101.31) scale(2.54951 7.97028)">
<stop stop-color="#C39BFF" stop-opacity="0.5"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint7_linear_64_3392" x1="93.5" y1="38" x2="55" y2="87.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#9CB1F6"/>
<stop offset="1" stop-color="#A2ABC6" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint8_linear_64_3392" x1="72.8657" y1="53.5" x2="6.00194" y2="52.812" gradientUnits="userSpaceOnUse">
<stop stop-color="#3578FF"/>
<stop offset="1" stop-color="#C0CAFF"/>
</linearGradient>
<linearGradient id="paint9_linear_64_3392" x1="65" y1="8" x2="31" y2="81.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#60EFFF" stop-opacity="0.82"/>
<stop offset="1" stop-color="#2A1FFF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint10_linear_64_3392" x1="22" y1="91.5" x2="73.5" y2="111" gradientUnits="userSpaceOnUse">
<stop stop-color="#49DEFF" stop-opacity="0"/>
<stop offset="1" stop-color="#DBFAFF" stop-opacity="0.57"/>
</linearGradient>
<linearGradient id="paint11_linear_64_3392" x1="26" y1="18" x2="15" y2="100" gradientUnits="userSpaceOnUse">
<stop stop-color="#78C6F7"/>
<stop offset="1" stop-color="#7099FF"/>
</linearGradient>
<linearGradient id="paint12_linear_64_3392" x1="34" y1="14.5" x2="31.489" y2="108.46" gradientUnits="userSpaceOnUse">
<stop stop-color="#60EFFF" stop-opacity="0.82"/>
<stop offset="1" stop-color="#B7ACFF" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint13_linear_64_3392" x1="39.2509" y1="43.4995" x2="39.2509" y2="81.0001" gradientUnits="userSpaceOnUse">
<stop stop-color="#F1FCFF"/>
<stop offset="0.606293" stop-color="#CFE5FF"/>
<stop offset="1" stop-color="#F1E4FF"/>
</linearGradient>
<linearGradient id="paint14_linear_64_3392" x1="35.5" y1="48" x2="52" y2="76" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#CAD4FF" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
