"use strict";(self["webpackChunkjoy_creator"]=self["webpackChunkjoy_creator"]||[]).push([[880],{7759:function(e,t,s){s.r(t),s.d(t,{default:function(){return V}});var a=function(){var e=this,t=e._self._c;return t("div",[e.config_data.layout==e.MENU_LAYOUT.LEFT&&e.layout?t("div",[t("header-and-left",[t("router-view")],1)],1):e.config_data.layout==e.MENU_LAYOUT.LEFTLAYOUT&&e.layout?t("div",[t("left-menu",{attrs:{isMicro:e.isMicro}},[t("router-view")],1)],1):t("div",{staticClass:"main-empty-menu"},[t("router-view"),t(e.microComponent,{tag:"component"})],1)])},i=[],n=function(){var e=this,t=e._self._c;return t("el-container",{staticClass:"app-box"},[t("el-aside",{staticClass:"app-left",attrs:{width:"72px"}},[t("div",{staticClass:"left-box"},[t("div",{staticClass:"app-logo-box"},[t("router-link",{staticClass:"app-logo",attrs:{underline:!1,to:"/"}},[t("img",{attrs:{src:e.appLogo,alt:"京东云低代码"}})])],1),t("div",{staticClass:"left-menu"},e._l(e.menuList,(function(s,a){return t("div",{key:a,staticClass:"menu-button",class:{activeMenu:e.menuPrimaryKeyReplace(e.activeMenu)==e.menuPrimaryKeyReplace(s)},on:{click:function(t){return e.setActiveMenu(s)}}},[t("div",{staticClass:"icon-box-wrap",style:{"background-color":s.iconColor}},[s.iconUrl?t("img",{attrs:{src:s.iconUrl}}):t("i",{class:[s.icon||"el-icon-menu"]})]),t("div",[e._v(e._s(s.menuName))])])})),0)]),t("div",{staticClass:"user-info-box"},[t("el-dropdown",{attrs:{placement:"bottom"},on:{command:e.userLogout}},[t("div",{staticClass:"user-info"},[t("el-link",{staticClass:"user-info-link",attrs:{underline:!1,title:`${e.user_data.fullName}(${e.user_data.userName})`}},[t("el-avatar",{attrs:{icon:"el-icon-user-solid",size:"small",src:e.headImg}})],1)],1),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",[e._v("退出登录")])],1)],1)],1)]),e.menuList&&e.menuList.length?t("el-container",{staticClass:"right-container"},[t("el-header",{staticClass:"app-header-container",staticStyle:{height:"48px"}},[t("div",{staticClass:"app-header"},[t("div",{staticClass:"menu-header"},[t("div",{staticClass:"title",style:e.activeMenu?.path||e.activeMenu?.pageKey?"cursor: pointer;":"",on:{click:e.titleClick}},[e._v(" "+e._s(e.activeMenu.menuName||"")+" ")]),t("el-menu",{staticClass:"el-menu-demo",attrs:{"default-active":e.defaultActive,mode:"horizontal"}},[t("menu-tree",{staticClass:"menu-li",attrs:{menuData:e.activeMenu}})],1)],1)])]),t("el-main",{staticClass:"app-main"},[t("left-breadcrumb",{attrs:{menuData:e.menuAllList,noRender:!0}}),e._t("default")],2)],1):e._e()],1)},l=[],r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"menu-wrap",style:{display:this.menuData.level>0?"block":"flex"}},e._l(e.menuDataChildren,(function(s){return t(s.children&&s.children.length>0?"el-submenu":"el-menu-item",{key:e.menuPrimaryKeyReplace(s),tag:"component",attrs:{index:`/${e.menuPrimaryKeyReplace(s)}`},scopedSlots:e._u([s.children&&s.children.length>0?{key:"title",fn:function(){return[t("span",{staticClass:"submenu-info",on:{click:function(t){return e.menuJump(s,e.$router)}}},[e._v(e._s(s.menuName))])]},proxy:!0}:null],null,!0)},[s.children&&s.children.length>0?t("menu-tree",{attrs:{menuData:s}}):s.menuStatus&&1==s.menuStatus?t("div",[t("span",{staticClass:"submenu-info",attrs:{slot:"title"},on:{click:function(t){return e.menuJump(s,e.$router)}},slot:"title"},[e._v(e._s(s.menuName))])]):e._e()],1)})),1)},u=[],o=s(7431),c=s(7795),h={props:["menuData"],name:"MenuTree",data:()=>({menuJump:o.mh,menuPrimaryKeyReplace:c.H9}),computed:{menuDataChildren(){return this.menuData.children?.map((e=>({...e,children:e.children?.filter((e=>0!==e.menuStatus))})))}}},d=h,m=s(6108),p=(0,m.A)(d,r,u,!1,null,"2ee9d80d",null),f=p.exports,v=function(){var e=this,t=e._self._c;return e.noRender?e._e():t("div",[e.menuList.length?[e._l(e.menuList,(function(s,a){return[a===e.menuList.length-1?t("el-breadcrumb-item",{key:s.path,staticClass:"breadcrumb_title isLast"},[e._v(" "+e._s(s.menuName)+" ")]):t("el-breadcrumb-item",{key:s.path,attrs:{to:s.path}},[t("span",{class:s.path||s.pageKey?"breadcrumb_a":"breadcrumb_title",on:{click:function(t){return e.resetRouter(s)}}},[e._v(e._s(s.menuName))])])]}))]:t("el-breadcrumb-item",[t("span",{staticClass:"breadcrumb_title isLast"},[e._v(e._s(e.title))])])],2)},_=[],g={props:["menuData","noRender"],name:"LeftBreadcrumb",data(){return{menuList:[],history:[],hadAppend:!1,title:document.title}},mounted(){this.$router._back=this.back},watch:{$route:{deep:!0,immediate:!0,handler(e,t){e.path&&t?.path&&e.path==t.path||this.routeHandler(e,t)}}},methods:{back(){const e=this.menuList[this.menuList.length-2]?.path;e?this.$router.push(e):this.$router.push("/")},getMenuList(e){const{params:t,path:s}=e,a=(0,c.mC)(this.$router,s)?s.replace(/^\//,""):t?.id;return(0,c.Il)(this.menuData,a)},routeHandler(e,t){const s=document.title;this.title=s;const{list:a,lastName:i,isAppend:n}=e?.params?.breadcrumb||{},l=this.getMenuList(e);let r=[],u=-1;if(this.menuList.forEach(((t,s)=>{t.path==e.fullPath&&(u=s)})),-1!=u)r=this.menuList.slice(0,u+1);else if(a)r=a;else if(n){r=this.menuList,r[r.length-1].path=t.fullPath;const a=l.length;r.push({path:e.fullPath,menuName:a?l[a-1].menuName:s})}else r=l;i&&(r[r.length-1].menuName=i),this.menuList=r},resetRouter(){}}},y=g,C=(0,m.A)(y,v,_,!1,null,"5964cca4",null),M=C.exports,L=s(4637),b=s.p+"__static/img/ver_10.25.05_logo.455a3342b90e0a9b.svg",k=s(7801),A=s(3244),$={data(){return{defaultActive:"",activeMenu:{},sourceActiveMenu:{},parentIdx:3,colorList:["#FF6F68","#00EDDC","#FFB22A","#00E08A","#FF8476"],userLogout:o.Ub,menuPrimaryKeyReplace:c.H9}},components:{MenuTree:f,LeftBreadcrumb:M},watch:{$route(){this.lockMenu(),this.$nextTick((()=>{document.getElementsByClassName("app-main")[0].scrollTo(0,0)}))},"$store.state.overlay_route"(e){this.lockMenu(e.pageKey)}},mounted(){this.menuList&&this.menuList.length>0&&this.lockMenu(),window.addEventListener("resize",this.getMenuCount),this.getMenuCount()},computed:{...(0,L.aH)(["user_data","showMenu","config_data","key_map","menus_data"]),headImg(){return this.user_data.headImg||k.k_},menuList(){return this.showMenu},menuAllList(){return this.menus_data.data},appLogo(){return this.config_data?.appLogo||b}},methods:{getMenuCount(){if(this.activeMenu.menuName){this.activeMenu=Object.assign({},this.sourceActiveMenu);let e=document.body.clientWidth-72-48-16*(this.activeMenu.menuName.length+1),t=Math.floor(e/138);this.activeMenu.children&&this.activeMenu.children.length>t&&this.loadMore(t)}},loadMore(e){let t=this.activeMenu.children,s=t.slice(e,t.length);if(0==this.activeMenu.children.filter((e=>"more"==e.menuCode)).length){let t={children:s.map((e=>e)),parentMenuCode:0,menuName:"更多",menuCode:"more",level:k.zw.CHILD};this.activeMenu.children=this.activeMenu.children.slice(0,e),this.activeMenu.children[e]=t}},setActiveMenu(e){this.sourceActiveMenu=e,this.activeMenu=Object.assign({},this.sourceActiveMenu),this.getMenuCount(),(0,o.mh)(e,this.$router)},lockMenu(e){this.defaultActive="";const t=this.$route.params?.id,s=Object.keys(this.key_map),a=t?s.find((e=>e===t||e===`/${t}`)):this.$route.path,i=(0,c.mC)(this.$route.path)?this.$route.path:a||(0,c.U0)(e||t);this.searchPage(this.menuAllList,i),this.defaultActive&&(this.sourceActiveMenu=this.menuAllList[this.parentIdx],this.activeMenu=Object.assign({},this.sourceActiveMenu))},searchPage(e,t,s=null){for(let a=0;a<e.length;a++){const i=e[a];"0"!==(0,c.NG)(i)&&(0,c.NG)(i)||i.level!=k.zw.ROOT||this.defaultActive||(this.parentIdx=a);const n=A.ZM.test(t)?"pageKey":"path";if(i[n]==t){0==i.menuStatus&&s?this.defaultActive=`/${(0,c.H9)(s)}`:this.defaultActive=`/${(0,c.H9)(i)}`;break}i.children&&this.searchPage(i.children,t,i)}},titleClick(){(this.activeMenu?.path||this.activeMenu?.pageKey)&&(0,o.mh)(this.activeMenu,this.$router)}}},w=$,x=(0,m.A)(w,n,l,!1,null,"5d4664ef",null),N=x.exports,F=function(){var e=this,t=e._self._c;return t("el-container",{staticClass:"app-box"},[t("el-aside",{staticClass:"app-left",attrs:{width:e.appLeftWidth}},[t("div",{staticClass:"left-box"},[e.isMicro?e._e():t("div",{class:"app-logo-box-"+(e.isCollapse?"is":"ollapse")},[t("div",{staticClass:"app-logo-box"},[t("router-link",{staticClass:"app-logo",attrs:{underline:!1,to:"/"}},[t("img",{attrs:{src:e.appLogo,alt:"京东云低代码"}})])],1),e.isCollapse?e._e():t("div",{staticClass:"app-name-box-is"},[e._v(e._s(e.appName))])]),t("div",{staticClass:"left-menu"},[t("el-menu",{staticClass:"el-menu-vertical",attrs:{"active-text-color":"#303133","text-color":"#606266","menu-trigger":"click","default-active":e.defaultActive,collapse:e.isCollapse,"collapse-transition":!1},on:{select:e.handleSelect}},[t("left-menu-tree",{attrs:{menuData:e.filterMenuData(e.menuList),"default-active":e.defaultActive}})],1)],1),t("div",{staticClass:"left-footer"},[e.isMicro?e._e():t("div",{class:e.isCollapse?"is-show-center is-hidde":"is-show-ollapse is-show"},[t("el-dropdown",{attrs:{placement:"top"},on:{command:e.userLogout}},[t("div",{staticClass:"user-info"},[t("el-link",{staticClass:"user-info-link",attrs:{underline:!1,title:`${e.user_data.fullName}(${e.user_data.userName})`}},[t("el-avatar",{attrs:{icon:"el-icon-user-solid",size:"small",src:e.headImg}})],1),e.isCollapse?e._e():t("el-link",{staticClass:"userName_nick",attrs:{underline:!1,title:e.user_data.fullName}},[e._v(e._s(e.user_data.fullName))])],1),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",[e._v("退出登录")])],1)],1)],1),t("div",{class:"is-show-"+(e.isCollapse?"center":"ollapse"),on:{click:e.isColl}},[t("i",{class:`el-icon-s-${e.isCollapse&&"un"}fold`}),e.isCollapse?e._e():t("span",{staticClass:"col-text"},[e._v("收起")])])])])]),t("el-container",{staticClass:"right-container"},[t("el-main",{staticClass:"app-main"},[e._t("default")],2)],1)],1)},D=[],H=function(){var e=this,t=e._self._c;return t("div",[e._l(e.menuData,(function(s){return[s.children&&s.children.length>0?t("el-submenu",{key:s.pageKey,class:e.defaultActive==`/${e.menuPrimaryKeyReplace(s)}`?"is-active":"",attrs:{index:`/${e.menuPrimaryKeyReplace(s)}`,"popper-class":"popperLine"}},[t("template",{slot:"title"},[s.iconUrl&&e.isIocn?t("img",{staticClass:"sub-info",style:{background:s.iconColor,color:"#FFF"},attrs:{src:s.iconUrl}}):e.isIocn?t("i",{staticClass:"sub-info",class:[s.icon||"el-icon-menu"],style:{background:s.iconColor,color:"#FFF"}}):e._e(),t("span",{staticStyle:{"margin-left":"9px"},attrs:{slot:"title"},on:{click:function(t){return e.handleSelect(s)}},slot:"title"},[e._v(e._s(s.menuName))])]),t("left-menu-tree",{attrs:{menuData:s.children,isIocn:!1,"default-active":e.defaultActive}})],2):s.menuStatus&&1==s.menuStatus?t("el-menu-item",{key:s.pageKey,attrs:{"popper-class":"popperLine",index:`/${e.menuPrimaryKeyReplace(s)}`}},[s.iconUrl&&e.isIocn?t("img",{staticClass:"sub-info",style:{background:s.iconColor,color:"#FFF"},attrs:{src:s.iconUrl}}):e.isIocn?t("i",{staticClass:"sub-info",class:[s.icon],style:{background:s.iconColor,color:"#FFF"}}):e._e(),t("span",{staticStyle:{"margin-left":"9px"},attrs:{slot:"title"},slot:"title"},[e._v(e._s(s.menuName))])]):e._e()]}))],2)},K=[],S={props:{menuData:{type:Array,default:()=>[]},isIocn:{type:Boolean,default:!0},defaultActive:{type:String,default:""}},name:"LeftMenuTree",data(){return{menuJump:o.mh,menuPrimaryKeyReplace:c.H9}},methods:{handleSelect(e){(0,o.mh)(e,this.$router)}}},P=S,T=(0,m.A)(P,H,K,!1,null,"3579736c",null),U=T.exports,I={props:{isMicro:{type:Boolean,default:!1}},data(){return{defaultActive:"",isCollapse:!1,userLogout:o.Ub}},components:{LeftMenuTree:U},watch:{$route(){this.lockMenu(),this.$nextTick((()=>{document.getElementsByClassName("app-main")[0]&&document.getElementsByClassName("app-main")[0].scrollTo(0,0)}))}},mounted(){this.menuList&&this.menuList.length>0&&this.lockMenu()},computed:{...(0,L.aH)(["user_data","config_data","showMenu","menus_data","key_map"]),headImg(){return this.user_data.headImg||k.k_},menuList(){return this.showMenu},menuAllList(){return this.menus_data.data},appLogo(){return this.config_data?.appLogo||b},appName(){return this.config_data?.appName||"京东云低代码平台"},appLeftWidth(){return this.isCollapse?"48px":"200px"}},methods:{isColl(){this.isCollapse=!this.isCollapse},filterMenuData(e){return e.filter((e=>1===e.menuStatus)).map((e=>e.children?{...e,children:this.filterMenuData(e.children)}:e))},lockMenu(){this.defaultActive="";const e=this.$route.params?.id,t=Object.keys(this.key_map),s=e&&"object"!==typeof e?t.find((t=>t===e||t===`/${e}`)):this.$route.path,a=(0,c.mC)(this.$route.path)?this.$route.path:s||(0,c.U0)(e);this.searchPage(this.menuAllList,a)},searchPage(e,t,s=null){for(let a=0;a<e.length;a++){const i=e[a],n=A.ZM.test(t)?"pageKey":"path";if(i[n]==t){0==i.menuStatus&&s?this.defaultActive=`/${(0,c.H9)(s)}`:this.defaultActive=`/${(0,c.H9)(i)}`;break}if(i.children){const e=i.level>1?s:i;this.searchPage(i.children,t,e)}}},handleSelect(e){let t=this.filterMenuData(this.menuList);this.filterMenuSelect(t,e)},filterMenuSelect(e,t){let s=null;return s=e.map((e=>{if(`/${(0,c.H9)(e)}`==t)return(0,o.mh)(e,this.$router),e;null!=e.children&&e.children?.length>0&&e.children&&this.filterMenuSelect(e.children,t)})),s}}},R=I,E=(0,m.A)(R,F,D,!1,null,"cf320b80",null),O=E.exports,j=function(){var e=this,t=e._self._c;return t("div")},B=[],z={name:"MicroApp",computed(){document.body.classList.add("hasMicroApp")}},Y=z,J=(0,m.A)(Y,j,B,!1,null,null,null),W=J.exports,Z=s(1811),G={name:"page-layout",data(){return{microComponent:null,layout:!0,hideMenuList:[],MENU_LAYOUT:k.Hf,isMicro:(0,Z.ww)()}},components:{HeaderAndLeft:N,LeftMenu:O},created(){if(!(0,Z.ZN)()&&this.isMicro&&(this.microComponent=W),this.isMicro)return void(this.layout=!1);let e=this.menus_data.data;this.setHideList(e),this.handleLayout(this.$route)},computed:{...(0,L.aH)(["config_data","menus_data","router_key"])},watch:{$route(e){this.handleLayout(e)}},methods:{handleLayout(e){let t="";e.path===`/${e.params.id}`?t=(0,c.U0)(e.params.id):this.hideMenuList.forEach((s=>{e.path.startsWith(`/${s}`)&&(t=s)})),this.setLayout(t)},setLayout(e){this.isMicro?this.layout=!1:this.config_data&&([k.Hf.LEFT,k.Hf.LEFTLAYOUT].includes(this.config_data.layout)?this.layout=!this.hideMenuList.includes(e):this.layout=!1)},setHideList(e){for(let t of e)t.showType&&(t.pageKey||t.path)&&this.hideMenuList.push(t.pageKey||t.path.replace(/^\//,"")),t.children&&null!==t.children&&this.setHideList(t.children)}}},q=G,Q=(0,m.A)(q,a,i,!1,null,null,null),V=Q.exports}}]);