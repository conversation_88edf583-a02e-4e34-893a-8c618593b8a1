"use strict";(self["webpackChunkjoy_creator"]=self["webpackChunkjoy_creator"]||[]).push([[386],{1240:function(t,a,e){e.r(a),e.d(a,{default:function(){return m}});var r=function(){var t=this,a=t._self._c;return a("div",{staticClass:"edit-page"},[a("TitlePage",[a("JoyTitleButtonBreadCrumb",{attrs:{modelName:t.formData.name},on:{handleReturn:t.handleBack}})],1),a("div",{staticClass:"edit-content"},[a("el-form",{staticClass:"detail-form",attrs:{model:t.formData,size:"small"}},[a("el-row",{staticStyle:{flex:"1",overflow:"hidden"},attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"文档分类",prop:"tag"}},[t._v(" "+t._s(t.formData.tag)+" ")])],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"文档概述",prop:"description"}},[t._v(" "+t._s(t.formData.description)+" ")])],1),a("el-col",{staticStyle:{height:"calc(100% - 48px)"},attrs:{span:24}},[a("editor",{ref:"editorRef",attrs:{value:t.formData.content,sessionId:t.detailId,isShowToolbar:!1,editable:!1,editorType:"detail"}})],1)],1)],1)],1)],1)},o=[],i=e(7967),l=e(6858),s={name:"TemplateLibraryForm",components:{Editor:i.A},data(){return{detailId:null,loading:!1,formData:{name:"",tag:"",description:"",content:""}}},mounted(){this.$route.query.detailId&&(this.detailId=this.$route.query.detailId,this.initData())},methods:{handleBack(){this.$router.go(-1)},handleCancel(){this.$router.go(-1)},initData(){(0,l.n2)({modelName:"template_library",id:this.detailId}).then((t=>{200===t.code&&(this.formData.name=t.data.name,this.formData.tag=t.data.tag,this.formData.description=t.data.description,this.formData.content=t.data.content)}))}}},n=s,d=e(6108),c=(0,d.A)(n,r,o,!1,null,"27f54f89",null),m=c.exports}}]);