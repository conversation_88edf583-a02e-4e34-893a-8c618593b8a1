"use strict";(self["webpackChunkjoy_creator"]=self["webpackChunkjoy_creator"]||[]).push([[875],{2607:function(t,e,n){n.d(e,{JO:function(){return d},Rm:function(){return r},T3:function(){return c},aA:function(){return l},um:function(){return a},wX:function(){return u}});var o=n(908),i=n.n(o),s=n(6858);const r=t=>i().$http.post(`${s.VY}/api/v1/chat-history/list`,t),a=t=>i().$http.get(`${s.VY}/api/v1/chat-history/dtl/${t.sessionId}`),l=t=>i().$http.get(`${s.VY}/api/v1/edit-document/dtl/${t.sessionId}`),c=t=>i().$http.post(`${s.VY}/api/v1/edit-document/save`,t),d=t=>i().$http.post(`${s.VY}/api/v1/chat-history/collection`,t),u=t=>i().$http.delete(`${s.VY}/api/v1/chat-history/del/${t.sessionId}`)},3002:function(t,e,n){n.d(e,{UV:function(){return d},V1:function(){return u},WY:function(){return m},XZ:function(){return f},Xv:function(){return r},bP:function(){return c},nw:function(){return a},ud:function(){return h},v5:function(){return p},zU:function(){return l}});var o=n(908),i=n.n(o),s=n(6858);const r=({params:t,onopen:e,onmessage:n,onerror:o,onclose:i})=>{(0,s.Wp)({url:`${s.VY}/api/v1/llm/chat/doron`,params:t,onopen:e,onmessage:n,onclose:i,onerror:o})},a=({params:t,onopen:e,onmessage:n,onerror:o,onclose:i})=>{(0,s.Wp)({url:`${s.VY}/api/v1/llm/chat/create`,params:t,onopen:e,onmessage:n,onerror:o,onclose:i})},l=({params:t,onopen:e,onmessage:n,onerror:o,onclose:i})=>{(0,s.Wp)({url:`${s.VY}/api/v1/llm/chat/rewrite`,params:t,onopen:e,onmessage:n,onerror:o,onclose:i})},c=()=>i().$http.post(`${s.VY}/api/v1/chat-history/create`),d=t=>i().$http.post(`${s.VY}/api/v1/chat-history/operation`,t),u=t=>i().$http.get(`${s.VY}/api/v1/dict/dtl/${t.dict}`),h=t=>i().$http.get(`${s.VY}/api/v1/suggest-govern/list`,t),p=t=>i().$http.get(`${s.VY}/api/v1/edit-document/convert-html/${t}`),f=t=>i().$http.post(`${s.VY}/api/v1/llm/chat/autofill`,t),m=t=>i().$http.post(`${s.VY}/api/v1/llm/chat/autofill-extraction`,t)},6858:function(t,e,n){async function o(t,e){const n=t.getReader();let o;while(!(o=await n.read()).done)e(o.value)}function i(t){let e,n,o,i=!1;return function(s){void 0===e?(e=s,n=0,o=-1):e=r(e,s);const a=e.length;let l=0;while(n<a){i&&(10===e[n]&&(l=++n),i=!1);let s=-1;for(;n<a&&-1===s;++n)switch(e[n]){case 58:-1===o&&(o=n-l);break;case 13:i=!0;case 10:s=n;break}if(-1===s)break;t(e.subarray(l,s),o),l=n,o=-1}l===a?e=void 0:0!==l&&(e=e.subarray(l),n-=l)}}function s(t,e,n){let o=a();const i=new TextDecoder;return function(s,r){if(0===s.length)null===n||void 0===n||n(o),o=a();else if(r>0){const n=i.decode(s.subarray(0,r)),a=r+(32===s[r+1]?2:1),l=i.decode(s.subarray(a));switch(n){case"data":o.data=o.data?o.data+"\n"+l:l;break;case"event":o.event=l;break;case"id":t(o.id=l);break;case"retry":const n=parseInt(l,10);isNaN(n)||e(o.retry=n);break}}}}function r(t,e){const n=new Uint8Array(t.length+e.length);return n.set(t),n.set(e,t.length),n}function a(){return{data:"",event:"",id:"",retry:void 0}}n.d(e,{VY:function(){return v},n2:function(){return b},Rh:function(){return $},Wp:function(){return g}});var l=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&e.indexOf(o)<0&&(n[o]=t[o]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(o=Object.getOwnPropertySymbols(t);i<o.length;i++)e.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(t,o[i])&&(n[o[i]]=t[o[i]])}return n};const c="text/event-stream",d=1e3,u="last-event-id";function h(t,e){var{signal:n,headers:r,onopen:a,onmessage:h,onclose:f,onerror:m,openWhenHidden:v,fetch:g}=e,$=l(e,["signal","headers","onopen","onmessage","onclose","onerror","openWhenHidden","fetch"]);return new Promise(((e,l)=>{const b=Object.assign({},r);let y;function w(){y.abort(),document.hidden||O()}b.accept||(b.accept=c),v||document.addEventListener("visibilitychange",w);let S=d,C=0;function V(){document.removeEventListener("visibilitychange",w),window.clearTimeout(C),y.abort()}null===n||void 0===n||n.addEventListener("abort",(()=>{V(),e()}));const R=null!==g&&void 0!==g?g:window.fetch,j=null!==a&&void 0!==a?a:p;async function O(){var n;y=new AbortController;try{const n=await R(t,Object.assign(Object.assign({},$),{headers:b,signal:y.signal}));await j(n),await o(n.body,i(s((t=>{t?b[u]=t:delete b[u]}),(t=>{S=t}),h))),null===f||void 0===f||f(),V(),e()}catch(r){if(!y.signal.aborted)try{const t=null!==(n=null===m||void 0===m?void 0:m(r))&&void 0!==n?n:S;window.clearTimeout(C),C=window.setTimeout(O,t)}catch(a){V(),l(a)}}}O()}))}function p(t){const e=t.headers.get("content-type");if(!(null===e||void 0===e?void 0:e.startsWith(c)))throw new Error(`Expected content-type to be ${c}, Actual: ${e}`)}var f=n(908),m=n.n(f);let v="";"app.dima.jd.com"!==location.hostname&&"joyb.jd.com"!==location.hostname||(v="http://joyedit-dev.jd.com",localStorage.setItem("baseURL",v));const g=({url:t,params:e,onopen:n,onmessage:o,onerror:i,onclose:s})=>{const r=new AbortController;h(t,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json","Cache-Control":"no-cache",Connection:"keep-alive",accept:"text/event-stream"},signal:r.signal,body:e,openWhenHidden:!0,onopen(){n&&n(r)},onmessage:t=>{o&&o(t)},onerror:t=>{i&&i(t),console.log("onerror:",t),r.abort()},onclose:()=>{s&&s()}})},$=t=>m().$http.post(`${v}/api/v1/model/${t.modelName}`,t),b=t=>m().$http.get(`${v}/api/v1/model/${t.modelName}/${t.id}`)},7967:function(t,e,n){n.d(e,{A:function(){return $}});var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"editor-container"},[e("joy-edit",{ref:"editorRef",attrs:{env:"dev",editorOptions:t.editorOptions},on:{load:t.handleLoad,save:t.handleSave,uploadFile:e=>t.$emit("uploadFile",e)}}),t.isWait?e("div",{staticClass:"loading-box"},[e("WriteLoading")],1):t._e(),t.loading?e("div",{staticClass:"opera-btn",on:{click:t.handleStop}},[e("i",{staticClass:"joyIcon icon-pause"}),e("p",[t._v("停止")])]):t._e(),t.isShowSuccess?e("div",{staticClass:"opera-btn"},[e("i",{staticClass:"joyIcon icon-chenggong"}),e("p",[t._v("写作完成")])]):t._e()],1)},i=[],s=n(3002),r=n(2607),a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"write-loading",class:{paused:t.isCancelled}},[e("el-image",{attrs:{src:t.LoadingSvg}}),t._m(0)],1)},l=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"dashed-lines-container"},[e("div",{staticClass:"line-segment",staticStyle:{"--segment-index":"0"}}),e("div",{staticClass:"line-segment",staticStyle:{"--segment-index":"1"}}),e("div",{staticClass:"line-segment",staticStyle:{"--segment-index":"2"}}),e("div",{staticClass:"line-segment",staticStyle:{"--segment-index":"3"}})])}],c=n.p+"__static/img/ver_10.25.05_loading.40f7a870be240f98.svg",d={name:"WriteLoading",data(){return{LoadingSvg:c,isCancelled:!1}}},u=d,h=n(6108),p=(0,h.A)(u,a,l,!1,null,"117a2f00",null),f=p.exports,m={name:"EditorComp",components:{WriteLoading:f},props:{value:{type:String,default:""},editable:{type:Boolean,default:!0},isShowToolbar:{type:Boolean,default:!0},sessionId:{type:String,default:""},isShowUpload:{type:Boolean,default:!1}},watch:{value(){this.$refs.editorRef&&this.$refs.editorRef.setContent(this.value,{isScrollBottom:!1})}},data(){return{loading:!1,isWait:!1,isShowSuccess:!1,editor:null,editorOptions:{editable:this.editable,isShowToolbar:this.isShowToolbar,ai:{enable:!0},base:{upload:{isShow:this.isShowUpload}}}}},mounted(){this.$refs.editorRef&&(this.$store.commit("setEditor",this.$refs.editorRef),this.$emit("editorInstance",this.$refs.editorRef))},methods:{handleLoad(){this.$refs.editorRef&&(this.$store.commit("setEditor",this.$refs.editorRef),this.$emit("editorInstance",this.$refs.editorRef))},toSearch(t,e){this.isWait=!0,this.loading=!0,this.$refs.editorRef.setContent("",{isEditable:!1});let n="",o=s.nw;"resetWrite"===e&&(o=s.zU),o({params:t,onopen:t=>{this.controller=t},onmessage:t=>{this.isWait&&(this.isWait=!1);const e=JSON.parse(t.data);if(n+=e.newContent,this.$refs.editorRef.setContent(n,{isEditable:e.finished}),this.forceStop)return this.controller.abort(),this.loading=!1,this.isShowSuccess=!1,void this.$refs.editorRef.setEditable(!0)},onerror:t=>{this.loading=!1,console.error(t)},onclose:()=>{this.loading=!1,setTimeout((()=>{this.isShowSuccess=!0,this.$refs.editorRef.setEditable(!0)}),0),setTimeout((()=>{this.isShowSuccess=!1}),2e3)}})},handleSave(t){if(!["writeDetail","chatHistoryForm"].includes(this.$route.name))return;const e=this.$refs.editorRef.getValue();(0,r.T3)({sessionId:this.sessionId,documentContent:t,textContent:e}).then((t=>{this.$eventBus.$emit("savePromise",t)})).catch((t=>{this.$eventBus.$emit("savePromise",t)}))},clearValue(){this.$refs.editorRef.setContent("")},handleStop(){this.controller&&this.controller.abort(),this.loading=!1},getHtml(){return this.$refs.editorRef.getHtml()}}},v=m,g=(0,h.A)(v,o,i,!1,null,null,null),$=g.exports}}]);