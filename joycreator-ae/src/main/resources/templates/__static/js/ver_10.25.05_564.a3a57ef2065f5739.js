"use strict";(self["webpackChunkjoy_creator"]=self["webpackChunkjoy_creator"]||[]).push([[564],{840:function(t,e,n){n.d(e,{J0:function(){return l},PE:function(){return s},Yq:function(){return o},as:function(){return a},m8:function(){return c},xs:function(){return r}});var i=n(5763);const o=(t,e="YYYY-MM-DD HH:mm:ss")=>{const n=new Date(t),i=n.getFullYear(),o=String(n.getMonth()+1).padStart(2,"0"),s=String(n.getDate()).padStart(2,"0"),a=String(n.getHours()).padStart(2,"0"),r=String(n.getMinutes()).padStart(2,"0"),l=String(n.getSeconds()).padStart(2,"0");return e.replace("YYYY",i).replace("MM",o).replace("DD",s).replace("HH",a).replace("mm",r).replace("ss",l)};function s(t,e){return new Promise(((n,i)=>{fetch(t,{responseType:"blob"}).then((t=>(t.ok||(i(t.status),console.error(`HTTP error! status: ${t.status}`)),t.blob()))).then((t=>{const i=window.URL.createObjectURL(t),o=document.createElement("a");o.href=i,o.download=e,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(i),n()})).catch((t=>{i(t),console.error("下载文件失败：",t)}))}))}const a=(t,e)=>{const{TEXT:n,FILE:o}=i.$z,s={msgType:n,chatContent:t.trim()},a=e[0]&&{msgType:o,id:e[0].id,fileId:e[0].id,fileVO:e[0]?.fileVO||e[0]};return[a,s].filter(Boolean)},r=t=>{if(!Array.isArray(t))return[];const e=[];return t.forEach((t=>{t.chatRole===i.Th.USER&&t.content.forEach((t=>{t.msgType===i.$z.FILE&&e.push(t)}))})),e},l=t=>t.map((t=>{let e="";if(t.chatRole===i.Th.USER)e=t.content||t.messageDetailList;else{let n=t.content.filter((t=>[i.Qy.STREAM].includes(t.chatRole))).map((t=>t.value)).join("。");e=[{msgType:i.$z.ASSISTANT,chatContent:n}]}return{chatRole:t.chatRole,messageDetailList:e}})),c=t=>({...t,erp:t.userName,name:t.realName,orgName:t.fullDetpName})},2607:function(t,e,n){n.d(e,{JO:function(){return d},Rm:function(){return a},T3:function(){return c},aA:function(){return l},um:function(){return r},wX:function(){return h}});var i=n(908),o=n.n(i),s=n(6858);const a=t=>o().$http.post(`${s.VY}/api/v1/chat-history/list`,t),r=t=>o().$http.get(`${s.VY}/api/v1/chat-history/dtl/${t.sessionId}`),l=t=>o().$http.get(`${s.VY}/api/v1/edit-document/dtl/${t.sessionId}`),c=t=>o().$http.post(`${s.VY}/api/v1/edit-document/save`,t),d=t=>o().$http.post(`${s.VY}/api/v1/chat-history/collection`,t),h=t=>o().$http.delete(`${s.VY}/api/v1/chat-history/del/${t.sessionId}`)},5763:function(t,e,n){n.d(e,{$z:function(){return o},Qy:function(){return s},Th:function(){return i}});const i={USER:"USER",ASSISTANT:"ASSISTANT"},o={TEXT:"TEXT",FILE:"FILE",ASSISTANT:"ASSISTANT"},s={STREAM:"ASSISTANT",TEMPLATE_LIBRARY:"TEMPLATE_LIBRARY",AUTO_FILL:"AUTO_FILL",TOOL:"TOOL",TEMPLATE_LIBRARY_QUERY:"TEMPLATE_LIBRARY_QUERY",DOCCONTENT_EXTRACTION_QUERY:"DOCCONTENT_EXTRACTION_QUERY",TEXT_REWRITE:"TEXT_REWRITE",DOCCONTENT_EXTRACTION_ERROR:"DOCCONTENT_EXTRACTION_ERROR",DOCCONTENT_EXTRACTION:"DOCCONTENT_EXTRACTION",DOCCONTENT_REWRITE:"DOCCONTENT_REWRITE",FREELANCE_WRITING:"FREELANCE_WRITING",FREELANCE_WRITING_START:"FREELANCE_WRITING_START",OUTLINE:"OUTLINE",TEXT_REPLACEMENT:"TEXT_REPLACEMENT"}},6858:function(t,e,n){async function i(t,e){const n=t.getReader();let i;while(!(i=await n.read()).done)e(i.value)}function o(t){let e,n,i,o=!1;return function(s){void 0===e?(e=s,n=0,i=-1):e=a(e,s);const r=e.length;let l=0;while(n<r){o&&(10===e[n]&&(l=++n),o=!1);let s=-1;for(;n<r&&-1===s;++n)switch(e[n]){case 58:-1===i&&(i=n-l);break;case 13:o=!0;case 10:s=n;break}if(-1===s)break;t(e.subarray(l,s),i),l=n,i=-1}l===r?e=void 0:0!==l&&(e=e.subarray(l),n-=l)}}function s(t,e,n){let i=r();const o=new TextDecoder;return function(s,a){if(0===s.length)null===n||void 0===n||n(i),i=r();else if(a>0){const n=o.decode(s.subarray(0,a)),r=a+(32===s[a+1]?2:1),l=o.decode(s.subarray(r));switch(n){case"data":i.data=i.data?i.data+"\n"+l:l;break;case"event":i.event=l;break;case"id":t(i.id=l);break;case"retry":const n=parseInt(l,10);isNaN(n)||e(i.retry=n);break}}}}function a(t,e){const n=new Uint8Array(t.length+e.length);return n.set(t),n.set(e,t.length),n}function r(){return{data:"",event:"",id:"",retry:void 0}}n.d(e,{VY:function(){return m},n2:function(){return g},Rh:function(){return y},Wp:function(){return v}});var l=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(i=Object.getOwnPropertySymbols(t);o<i.length;o++)e.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(t,i[o])&&(n[i[o]]=t[i[o]])}return n};const c="text/event-stream",d=1e3,h="last-event-id";function u(t,e){var{signal:n,headers:a,onopen:r,onmessage:u,onclose:T,onerror:f,openWhenHidden:m,fetch:v}=e,y=l(e,["signal","headers","onopen","onmessage","onclose","onerror","openWhenHidden","fetch"]);return new Promise(((e,l)=>{const g=Object.assign({},a);let E;function C(){E.abort(),document.hidden||L()}g.accept||(g.accept=c),m||document.addEventListener("visibilitychange",C);let w=d,R=0;function b(){document.removeEventListener("visibilitychange",C),window.clearTimeout(R),E.abort()}null===n||void 0===n||n.addEventListener("abort",(()=>{b(),e()}));const O=null!==v&&void 0!==v?v:window.fetch,N=null!==r&&void 0!==r?r:p;async function L(){var n;E=new AbortController;try{const n=await O(t,Object.assign(Object.assign({},y),{headers:g,signal:E.signal}));await N(n),await i(n.body,o(s((t=>{t?g[h]=t:delete g[h]}),(t=>{w=t}),u))),null===T||void 0===T||T(),b(),e()}catch(a){if(!E.signal.aborted)try{const t=null!==(n=null===f||void 0===f?void 0:f(a))&&void 0!==n?n:w;window.clearTimeout(R),R=window.setTimeout(L,t)}catch(r){b(),l(r)}}}L()}))}function p(t){const e=t.headers.get("content-type");if(!(null===e||void 0===e?void 0:e.startsWith(c)))throw new Error(`Expected content-type to be ${c}, Actual: ${e}`)}var T=n(908),f=n.n(T);let m="";"app.dima.jd.com"!==location.hostname&&"joyb.jd.com"!==location.hostname||(m="http://joyedit-dev.jd.com",localStorage.setItem("baseURL",m));const v=({url:t,params:e,onopen:n,onmessage:i,onerror:o,onclose:s})=>{const a=new AbortController;u(t,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json","Cache-Control":"no-cache",Connection:"keep-alive",accept:"text/event-stream"},signal:a.signal,body:e,openWhenHidden:!0,onopen(){n&&n(a)},onmessage:t=>{i&&i(t)},onerror:t=>{o&&o(t),console.log("onerror:",t),a.abort()},onclose:()=>{s&&s()}})},y=t=>f().$http.post(`${m}/api/v1/model/${t.modelName}`,t),g=t=>f().$http.get(`${m}/api/v1/model/${t.modelName}/${t.id}`)},8394:function(t,e,n){n.r(e),n.d(e,{default:function(){return p}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"chat_history list-page"},[e("p",{staticClass:"page-title"},[t._v("我的文档")]),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingList,expression:"loadingList"}],staticClass:"page-content"},[0===t.historyList.length?e("el-empty",{staticStyle:{height:"100%"},attrs:{image:"http://s3.cn-north-1.jdcloud-oss.com/jz-material/lcapicon/1658309481895-3905270251763847417.png"}}):t._e(),t.historyList.length>0?e("div",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:t.loadData,expression:"loadData"}],staticClass:"history-list scroll-box",attrs:{"infinite-scroll-distance":10,"infinite-scroll-immediate":!1,"infinite-scroll-disabled":t.loadingList}},t._l(t.historyList,(function(n,i){return e("div",{key:n.id,staticClass:"history-item"},[e("div",{staticClass:"item-header",on:{click:function(e){return t.handleEdit(n)}}},[e("el-image",{staticClass:"doc-icon",attrs:{src:t.documentIllustrationsSrc,alt:"文档图标"}}),e("div",{staticClass:"doc-info"},[e("h3",[e("TooltipText",{attrs:{text:n.name||"会话记录"}})],1),e("div",{staticClass:"doc-second-info"},[e("div",{staticStyle:{display:"flex"},on:{click:function(t){t.stopPropagation()}}},[n.jdrUserVO.userName?e("joy-erp",{staticStyle:{"margin-left":"-4px"},attrs:{data:t.handleUserInfo(n.jdrUserVO),avatar:"",vertical:"","avatar-size":24}}):t._e()],1),e("p",[t._v(t._s(t.formatDate(n.createTime)))])])]),e("div",{staticClass:"actions"},[e("i",{staticClass:"joyIcon icon-shanchu",on:{click:function(e){return e.stopPropagation(),t.handleDelete(n,i)}}})])],1),e("div",{staticClass:"related-files"},[e("p",{staticClass:"files-title"},[t._v("相关文件 ("+t._s(n.chatHistoryDetailTemplateList?.length||0)+")")]),e("div",{staticClass:"file-list-box"},[n.chatHistoryDetailTemplateList?.length?e("div",{staticClass:"file-list"},t._l(n.chatHistoryDetailTemplateList,(function(n,i){return e("div",{key:i,staticClass:"file-item",on:{click:function(e){return t.handleToTemplateDetail(n)}}},[e("TooltipText",{staticClass:"file-name",attrs:{text:n.templateName}})],1)})),0):t._e()])])])})),0):t._e()],1)])},o=[],s=n.p+"__static/img/ver_10.25.05_document-illustrations.f5007e05d7e2643b.jpeg",a=n(8903),r=n(2607),l=n(840),c={name:"ChatHistory",components:{TooltipText:a.A},data(){return{handleUserInfo:l.m8,formatDate:l.Yq,documentIllustrationsSrc:s,queryParams:{page:{current:1,size:10},param:""},hoverObj:{},loadingList:!1,total:0,historyList:[]}},mounted(){this.initData()},methods:{initData(){this.loadingList=!0,(0,r.Rm)(this.queryParams).then((t=>{200===t.code&&(this.historyList=this.historyList.concat(t.data.records),this.total=t.data.total)})).finally((()=>{this.loadingList=!1}))},loadData(){this.total>this.historyList.length&&(this.queryParams.page.current++,this.initData())},handleEdit(t){this.$router.push("/chat_history/form?sessionId="+t.sessionId)},handleCollection(t){(0,r.JO)({sessionId:t.sessionId,collection:!t.collection}).then((e=>{200===e.code&&this.$set(t,"collection",!t.collection)}))},handleDelete(t,e){this.$confirm("本条会话数据将被永久删除，不可恢复及撤销。确定要删除吗？","删除会话",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",beforeClose:(n,i,o)=>{"confirm"===n?(i.confirmButtonLoading=!0,i.confirmButtonText="执行中...",(0,r.wX)({sessionId:t.sessionId}).then((t=>{200===t.code&&(o(),this.$message.success("删除成功"),this.historyList.splice(e,1))})).finally((()=>{i.confirmButtonLoading=!1,i.confirmButtonText="确定"}))):o()}}).catch((()=>{}))},handleToTemplateDetail(t){const e=this.$router.resolve({path:"/template_library/detail",query:{detailId:t.templateId}});window.open(e.href)}}},d=c,h=n(6108),u=(0,h.A)(d,i,o,!1,null,"32ee8025",null),p=u.exports},8903:function(t,e,n){n.d(e,{A:function(){return c}});var i=function(){var t=this,e=t._self._c;return e("el-tooltip",{attrs:{content:t.text,disabled:!t.showTooltip,effect:"dark","popper-class":"tooltip-text-box"}},[e("div",{ref:"textContainer",staticClass:"tooltip-text",style:t.textStyle},[t._v(" "+t._s(t.displayText)+" ")])])},o=[],s={name:"TooltipText",props:{text:{type:String,required:!0},lineNum:{type:Number,default:1},lineHeight:{type:Number,default:20}},data(){return{showTooltip:!1,displayText:this.text,resizeObserver:null}},computed:{textStyle(){const t={"line-height":`${this.lineHeight}px`,"word-break":"break-all"};return 1===this.lineNum&&(t["white-space"]="nowrap",t["overflow"]="hidden",t["text-overflow"]="ellipsis"),t}},watch:{text:{handler(){this.startOverflowCheck()},immediate:!0},lineNum:{handler(){this.startOverflowCheck()},immediate:!0},lineHeight:{handler(){this.startOverflowCheck()},immediate:!0}},mounted(){window.addEventListener("resize",this.startOverflowCheck)},beforeDestroy(){window.removeEventListener("resize",this.startOverflowCheck)},methods:{startOverflowCheck(){this.displayText=this.text,this.showTooltip=!1,this.$nextTick((()=>{this.measureAndTruncate()}))},measureAndTruncate(){const t=this.$refs.textContainer;if(!t)return;if(1===this.lineNum)return void(this.showTooltip=t.scrollWidth>t.clientWidth);const e=this.lineHeight*this.lineNum;if(t.scrollHeight<=e)return void(this.showTooltip=!1);this.showTooltip=!0;let n=0,i=this.text.length,o=0;const s=()=>{if(n>i)return void(this.displayText=this.text.substring(0,o)+"...");const a=Math.floor((n+i)/2);this.displayText=this.text.substring(0,a)+"...",this.$nextTick((()=>{t.scrollHeight>e?i=a-1:(o=a,n=a+1),s()}))};s()}}},a=s,r=n(6108),l=(0,r.A)(a,i,o,!1,null,"5649a5c0",null),c=l.exports}}]);