server:
  port: 8091
logging:
  config: classpath:log/log4j2.yaml
app4s:
  application:
    id: '2943'
  datahub:
    open: false
    env: dev
  zk:
    url: testpubli.zk.jddb.com:2181
    namespace: publictest
    userName: admin
    password: admin888
  useCodeTemplate: false
  permissionInterceptor: com.jd.jdt.app4s.permission.embed.interceptor.JoyBuilderPermissionEmbedInterceptor
  permissionType: IPMS
  permissionAutoSave: user
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driverClassName: com.p6spy.engine.spy.P6SpyDriver
      url: *****************************************************************************************************************************************************************************************************************************
      username: joy_creator
      password: Ed8XjEGtRaHeIfTRGcoBUw==
workflow:
  alias: pro-operateFlowApi:0.0.1
rpc:
  llm:
    url: http://gpt-proxy.jd.com
    apiKey: aa051d32-db0b-4294-98a0-b02040240d48
    jdcloudPin: jcloud_doNGoJh
  office:
    url: http://joyoffice.jd.com
  aspose:
    url: https://convert.jd.com

oss:
  accessKey: A25E92A1A3D6433D8033EBF93307F2B0
  secretKey: 7A1C958DC39F415EA4C7631D98C1A83E
  endpoint: https://s3.jdpay.com
  intranetEndpoint: http://s3-internal-office-tech-north-1.jdfmgt.com
  bucket: joycreator
  protocol: http

r2m:
  appName: xinghui_r2m
  3cConnectionStr: r2m3c.jdfin.local
  3cToken: a4bc676ca4
  password: UiZ5p7e1MfgMqVX3KnYxS5JcV+Y376kCr2iQ4OsNmYU=

# IP白名单配置，多个IP用逗号分隔
ip:
  whitelist: ************