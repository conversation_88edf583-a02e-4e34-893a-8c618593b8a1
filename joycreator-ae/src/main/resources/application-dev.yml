server:
  port: 8091
logging:
  config: classpath:log/log4j2.yaml
app4s:
  application:
    id: '2943'
  datahub:
    open: false
    env: dev
  zk:
    url: testpubli.zk.jddb.com:2181
    namespace: publictest
    userName: admin
    password: admin888
  useCodeTemplate: false
  permissionType: IPMS
  permissionInterceptor: com.jd.jdt.app4s.permission.embed.interceptor.JoyBuilderPermissionEmbedInterceptor
  permissionAutoSave: user
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driverClassName: com.p6spy.engine.spy.P6SpyDriver
      url: ***********************************************************************************************************************************************************************************************************************************************
      username: on_apaas_dev_ddl
      password: X15RCWfoS0KIoaixa6jzBA==
workflow:
  alias: test-operateFlowApi:0.0.1
file:
  endpoint: s3.cn-north-1.jdcloud-oss.com
  bucket: oss-joybuilder
  accessKey: JDC_585715941E261CEB3F4836A8F9D7
  secretKey: 5BC227332D86536A1713E8A750854259
rpc:
  llm:
    url: http://gpt-proxy.jd.com
    apiKey: aa051d32-db0b-4294-98a0-b02040240d48
    jdcloudPin: jcloud_doNGoJh
  office:
    url: http://joyoffice.jd.com
  aspose:
    url: https://convert.jd.com

oss:
  accessKey: RE3LMZKZ4PD833HFTLYKD2XP3LH46GO0
  secretKey: 7KQWTOYZBJZ9WWNIK9J8PBCQQXR4KNYK
  endpoint: https://s3.jdpay.com
  intranetEndpoint: http://s3-internal-office-tech-north-1.jdfmgt.com
  bucket: joycreator-pre
  protocol: http

r2m:
  appName: xinghui_r2m
  3cConnectionStr: r2m3c.jdfin.local
  3cToken: a4bc676ca4
  password: UiZ5p7e1MfgMqVX3KnYxS5JcV+Y376kCr2iQ4OsNmYU=


# IP白名单配置，多个IP用逗号分隔
ip:
  whitelist: ************