package com.jd.jdt.joycreator.ae.service;

import com.jd.jdt.joycreator.ae.entity.PromptWordsConfig;
import com.jd.jdt.joycreator.ae.pojo.dto.*;
import com.jd.jdt.joycreator.ae.pojo.vo.AutofillExtractionVO;
import com.jd.jdt.joycreator.ae.pojo.vo.AutofillVO;
import feign.Response;
import reactor.core.publisher.Flux;

import java.util.List;

public interface ChatStudioService {

    /**
     * 模型网关聊天完成接口/流式
     *
     * @param promptWordsConfig
     * @return
     */
    Response chatCompletionsStream(PromptWordsConfig promptWordsConfig);

    /**
     * 模型网关聊天完成接口/非流式
     *
     * @param promptWordsConfig
     * @return
     */
    ChatCompletionResponseDTO chatCompletions(PromptWordsConfig promptWordsConfig);

    ChatCompletionResponseDTO chatCompletions(PromptWordsConfigDTO promptWordsConfigDTO);

    /**
     * 多伦聊天（流式）
     *
     * @param doronChatDTO
     * @return
     */
    Object doronChat(DoronChatDTO doronChatDTO);

    /**
     * 文档起草（流式）
     *
     * @param documentWritingDTO
     * @return
     */
    Flux<String> chatCreate(DocumentWritingDTO documentWritingDTO);

    Flux<String> processText(TextProcessingDTO textProcessingDTO);

    /**
     * 局部改写（流式）
     *
     * @param chatRewriteDocumentDTO
     * @return
     */
    Flux<String> chatRewrite(ChatRewriteDocumentDTO chatRewriteDocumentDTO);

    /**
     * 模版自动补全识别
     *
     * @param autofillDTO
     * @return
     */
    List<AutofillVO> autofill(AutofillDTO autofillDTO);

    /**
     * 模版自动补全原文提取
     *
     * @param toolDtlDTO
     * @return
     */
    AutofillExtractionVO autofillExtraction(ToolDtlDTO toolDtlDTO);

    /**
     * 文档起草命名
     *
     * @param userIntentDTO
     * @return
     */
    String chatCreateTheme(UserIntentDTO userIntentDTO);
}
