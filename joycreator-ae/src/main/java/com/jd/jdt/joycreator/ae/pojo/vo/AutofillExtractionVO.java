package com.jd.jdt.joycreator.ae.pojo.vo;

import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import com.jd.jdt.joycreator.ae.enums.ToolLabelsEnum;
import lombok.Data;

/**
 * <p>
 * AutofillExtractionVO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
public class AutofillExtractionVO {

    /**
     * 重写类型
     */
    public ToolLabelsEnum rewriteType;

    /**
     * 原始内容
     */
    private String oldContent;

    /**
     * 新内容
     */
    private String newContent;

    /**
     * 原文片段
     */
    private String originalParagraph;

    /**
     * 任务反馈描述
     */
    private String taskFeedback;
}
