package com.jd.jdt.joycreator.ae.service.impl;

import com.jd.jdt.joycreator.ae.pojo.dto.ChatCompletionChunkDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.ChatCompletionChunkVO;
import com.jd.jdt.joycreator.ae.service.ConversationService;
import com.jd.jdt.joycreator.ae.service.ChatStudioService;
import com.jd.jdt.joycreator.ae.pojo.dto.ConversationMessage;
import com.jd.jdt.joycreator.ae.pojo.dto.MessageContext;
import com.jd.jdt.joycreator.ae.pojo.dto.MessageIntent;
import com.jd.jsf.gd.util.JsonUtils;
import feign.Response;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 会话服务实现类
 * 负责处理用户消息，分析意图，生成澄清问题，执行工具，以及生成最终响应
 */
@Log4j2
@Service
public class ConversationServiceImpl implements ConversationService {

    @Autowired
    private ChatStudioService editService;

    /**
     * 意图检测提示词
     * 用于引导大语言模型分析用户输入，识别用户的写作意图和需求
     */
    private static final String INTENT_DETECTION_PROMPT =
            "你是 JoyCreator 智能写作助手，一位专业的写作任务分析专家。现在我需要你分析用户的写作需求，识别其意图和关键信息。\n\n" +
                    "请对以下用户输入进行深度分析：\n" +
                    "1. 主要写作意图（例如：创建商务合同、设计营销文案、编写产品说明书等）\n" +
                    "2. 写作类型（例如：说明文、议论文、应用文等）\n" +
                    "3. 写作目标和期望效果（例如：说服客户、传达信息、解释概念等）\n" +
                    "4. 写作所需具体参数（例如：合同类型、文档格式、目标受众、关键内容要点等）\n" +
                    "5. 是否需要进一步澄清（如果缺少关键信息，请列出需要澄清的具体点）\n" +
                    "6. 完成此写作任务可能需要使用的工具（例如：模板库查询、样例参考、规范检查等）\n\n" +
                    "分析过程：\n" +
                    "- 理解用户需求的核心目标和约束\n" +
                    "- 评估已提供信息的完整性和清晰度\n" +
                    "- 判断是否需要额外信息来完成任务\n\n" +
                    "请输出结构化的分析结果，包含以上所有要点。\n\n" +
                    "用户输入：%s";

    /**
     * 澄清问题生成提示词
     * 用于引导大语言模型根据已识别的意图，生成有针对性的澄清问题
     */
    private static final String CLARIFICATION_PROMPT =
            "你是 JoyCreator 智能写作助手，一位专注细节的写作顾问。我已经对用户的写作需求进行了初步分析，但需要进一步澄清一些关键信息，以便提供最精准的写作支持。\n\n" +
                    "基于以下已识别的信息，请生成必要的澄清问题：\n\n" +
                    "已识别的写作意图：%s\n" +
                    "已知参数和信息：%s\n" +
                    "需要澄清的要点：%s\n\n" +
                    "请遵循以下原则生成澄清问题：\n" +
                    "1. 问题应该简洁明了，每个问题聚焦于一个具体信息点\n" +
                    "2. 问题应当有逻辑顺序，从基础信息到细节信息递进\n" +
                    "3. 提供适当的选项或示例，帮助用户理解所需信息类型\n" +
                    "4. 解释为什么这些信息对完成写作任务至关重要\n" +
                    "5. 使用友好、专业的语气\n\n" +
                    "请生成3-5个关键澄清问题，确保能够获取完成写作任务所需的全部信息。";

    /**
     * 处理用户消息
     * 对用户输入进行意图检测，根据需要生成澄清问题或执行工具，最终生成响应
     *
     * @param userMessage 用户消息实体
     * @return 响应内容流
     */
    @Override
    public Flux<String> processUserMessage(ConversationMessage userMessage) {
        // 1. 检测用户意图
        MessageIntent intent = detectIntent(userMessage.getContent());
        userMessage.setIntent(intent);

        // 2. 如果需要澄清，生成澄清问题
        if (Boolean.TRUE.equals(intent.getNeedsClarification())) {
            return Flux.just(JsonUtils.toJSONString(generateClarifyingQuestions(intent)));
        }

        // 3. 如果需要执行工具，执行相应工具
        MessageContext context = userMessage.getContext();
        if (intent.getRequiredTools() != null && intent.getRequiredTools().length > 0) {
            Map<String, Object> toolResults = executeTools(intent, context);
            context.setToolResults(toolResults);
        }

        // 4. 生成最终响应
        return generateResponse(intent, context);
    }

    /**
     * 检测用户意图
     * 根据用户输入内容，分析并识别用户的意图、参数、是否需要澄清等信息
     *
     * @param messageContent 用户输入的消息内容
     * @return 结构化的意图对象
     */
    @Override
    public MessageIntent detectIntent(String messageContent) {
        // 格式化意图检测提示词
        String prompt = String.format(INTENT_DETECTION_PROMPT, messageContent);

        Queue<String> contentBuffer = new ConcurrentLinkedQueue<>();
        AtomicBoolean done = new AtomicBoolean(false);

        // 调用大语言模型进行意图检测
//        Response response = editService.chatCompletionsStream(prompt, "gpt-4o-0806");
        Response response = editService.chatCompletionsStream(null);
        StringBuilder result = new StringBuilder();

        try {
            // 读取并解析大语言模型的流式响应
            BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().asInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isEmpty(line)) continue;
                if (line.contains("data: [DONE]")) {
                    done.set(true);
                    break;
                }
                if (line.startsWith("data: ")) {
                    line = line.substring(6);
                }
                ChatCompletionChunkDTO chunk = JsonUtils.parseObject(line, ChatCompletionChunkDTO.class);
                if (chunk != null && CollectionUtils.isNotEmpty(chunk.getChoices())) {
                    for (ChatCompletionChunkDTO.Choice choice : chunk.getChoices()) {
                        if (choice != null && choice.getDelta().getContent() != null) {
                            result.append(choice.getDelta().getContent());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("意图检测出错", e);
        }

        // 解析大语言模型响应，构建结构化意图对象
        MessageIntent intent = new MessageIntent();
        String resultStr = result.toString();

        try {
            // 尝试首先以JSON格式解析（如果模型输出结构化JSON）
            try {
                Map<String, Object> jsonResult = JsonUtils.parseObject(resultStr, Map.class);
                if (jsonResult != null) {
                    if (jsonResult.containsKey("intentType")) {
                        intent.setIntentType((String) jsonResult.get("intentType"));
                    }
                    if (jsonResult.containsKey("confidence")) {
                        Object confidence = jsonResult.get("confidence");
                        if (confidence instanceof Number) {
                            intent.setConfidence(((Number) confidence).doubleValue());
                        }
                    }
                    if (jsonResult.containsKey("parameters")) {
                        intent.setParameters((Map<String, Object>) jsonResult.get("parameters"));
                    }
                    if (jsonResult.containsKey("needsClarification")) {
                        intent.setNeedsClarification((Boolean) jsonResult.get("needsClarification"));
                    }
                    if (jsonResult.containsKey("clarificationPoints")) {
                        Object points = jsonResult.get("clarificationPoints");
                        if (points instanceof Collection) {
                            intent.setClarificationPoints(((Collection<String>) points).toArray(new String[0]));
                        }
                    }
                    if (jsonResult.containsKey("requiredTools")) {
                        Object tools = jsonResult.get("requiredTools");
                        if (tools instanceof Collection) {
                            intent.setRequiredTools(((Collection<String>) tools).toArray(new String[0]));
                        }
                    }
                    return intent;
                }
            } catch (Exception e) {
                log.debug("响应不是JSON格式，尝试文本解析", e);
            }

            // 如果不是JSON格式，回退到文本解析
            Map<String, Object> parameters = new HashMap<>();
            List<String> clarificationPoints = new ArrayList<>();
            List<String> requiredTools = new ArrayList<>();

            // 按行解析文本响应
            String[] lines = resultStr.split("\n");
            for (String line : lines) {
                line = line.trim();

                // 提取意图类型
                if (line.contains("主要写作意图") || line.contains("写作意图")) {
                    String[] parts = line.split("[:：]", 2);
                    if (parts.length > 1) {
                        intent.setIntentType(parts[1].trim());
                    }
                }

                // 提取参数
                else if (line.contains("写作类型") || line.contains("写作目标") ||
                        line.contains("目标受众") || line.contains("关键内容")) {
                    String[] parts = line.split("[:：]", 2);
                    if (parts.length > 1) {
                        parameters.put(parts[0].trim(), parts[1].trim());
                    }
                }

                // 提取是否需要澄清
                else if (line.contains("需要澄清") || line.contains("需要进一步澄清")) {
                    String[] parts = line.split("[:：]", 2);
                    if (parts.length > 1) {
                        String value = parts[1].trim().toLowerCase();
                        intent.setNeedsClarification(value.contains("是") || value.contains("需要") || value.contains("yes") || value.contains("true"));
                    }
                }

                // 提取澄清点
                else if (line.contains("澄清点") || line.contains("澄清的具体点")) {
                    String[] parts = line.split("[:：]", 2);
                    if (parts.length > 1) {
                        String[] points = parts[1].split("[,，;；]");
                        for (String point : points) {
                            clarificationPoints.add(point.trim());
                        }
                    }
                }

                // 提取所需工具
                else if (line.contains("需要工具") || line.contains("可能需要使用的工具")) {
                    String[] parts = line.split("[:：]", 2);
                    if (parts.length > 1) {
                        String[] tools = parts[1].split("[,，;；]");
                        for (String tool : tools) {
                            requiredTools.add(tool.trim());
                        }
                    }
                }
            }

            // 设置解析结果
            intent.setParameters(parameters);
            intent.setClarificationPoints(clarificationPoints.toArray(new String[0]));
            intent.setRequiredTools(requiredTools.toArray(new String[0]));

            // 如果未明确设置置信度，使用默认值
            if (intent.getConfidence() == null) {
                intent.setConfidence(0.8);
            }

            // 如果未明确设置是否需要澄清，根据澄清点判断
            if (intent.getNeedsClarification() == null) {
                intent.setNeedsClarification(!clarificationPoints.isEmpty());
            }
        } catch (Exception e) {
            // 意图解析出错时的错误处理
            log.error("解析意图检测响应出错", e);
            intent.setIntentType("UNKNOWN");
            intent.setNeedsClarification(true);
            intent.setClarificationPoints(new String[]{"无法解析用户意图，需要用户澄清"});
        }

        return intent;
    }

    /**
     * 生成澄清问题
     * 根据已识别的意图生成澄清问题，帮助获取完成任务所需的全部信息
     *
     * @param intent 已识别的意图对象
     * @return 包含澄清问题的会话消息
     */
    @Override
    public ConversationMessage generateClarifyingQuestions(MessageIntent intent) {
        // 格式化澄清问题提示词
        String prompt = String.format(
                CLARIFICATION_PROMPT,
                intent.getIntentType(),
                JsonUtils.toJSONString(intent.getParameters()),
                String.join(", ", intent.getClarificationPoints())
        );

        // 创建澄清问题消息
        ConversationMessage clarificationMessage = new ConversationMessage();
        clarificationMessage.setRole("assistant");
        clarificationMessage.setTimestamp(System.currentTimeMillis());

        // 调用大语言模型生成澄清问题
//        Response response = editService.chatCompletionsStream(prompt, "gpt-4o-0806");
        Response response = editService.chatCompletionsStream(null);
        StringBuilder content = new StringBuilder();

        try {
            // 读取并解析大语言模型的流式响应
            BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().asInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isEmpty(line)) continue;
                if (line.contains("data: [DONE]")) break;
                if (line.startsWith("data: ")) {
                    line = line.substring(6);
                }
                ChatCompletionChunkDTO chunk = JsonUtils.parseObject(line, ChatCompletionChunkDTO.class);
                if (chunk != null && CollectionUtils.isNotEmpty(chunk.getChoices())) {
                    for (ChatCompletionChunkDTO.Choice choice : chunk.getChoices()) {
                        if (choice != null && choice.getDelta().getContent() != null) {
                            content.append(choice.getDelta().getContent());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("生成澄清问题出错", e);
        }

        // 设置澄清问题内容
        clarificationMessage.setContent(content.toString());
        return clarificationMessage;
    }

    /**
     * 执行工具
     * 根据意图和上下文执行必要的工具，获取完成任务所需的资源或信息
     *
     * @param intent  用户意图
     * @param context 消息上下文
     * @return 工具执行结果
     */
    @Override
    public Map<String, Object> executeTools(MessageIntent intent, MessageContext context) {
        Map<String, Object> results = new HashMap<>();

        // 示例：如果意图需要查询合同模板
        if (Arrays.asList(intent.getRequiredTools()).contains("CONTRACT_TEMPLATE_LOOKUP")) {
            // TODO: 实现合同模板查询逻辑
            // 这里将涉及调用模板服务/API
            // results.put("contractTemplates", templateService.findTemplates(intent.getParameters()));
        }

        return results;
    }

    /**
     * 生成最终响应
     * 根据意图和上下文，生成满足用户需求的写作内容
     *
     * @param intent  用户意图
     * @param context 消息上下文
     * @return 响应内容流
     */
    @Override
    public Flux<String> generateResponse(MessageIntent intent, MessageContext context) {
        // 构建基于意图和上下文的提示词
        StringBuilder promptBuilder = new StringBuilder();
        promptBuilder.append("你是 JoyCreator 智能写作助手，一位专业的内容创作专家。请基于以下分析信息，生成满足用户需求的写作内容：\n\n");
        promptBuilder.append("1. 用户写作意图：").append(intent.getIntentType()).append("\n");
        promptBuilder.append("2. 写作参数和要求：").append(JsonUtils.toJSONString(intent.getParameters())).append("\n");

        // 添加工具执行结果（如果有）
        if (context.getToolResults() != null && !context.getToolResults().isEmpty()) {
            promptBuilder.append("3. 相关参考资料和工具结果：").append(JsonUtils.toJSONString(context.getToolResults())).append("\n");
        }

        // 添加用户选择的资源（如果有）
        if (context.getSelectedResources() != null && !context.getSelectedResources().isEmpty()) {
            promptBuilder.append("4. 用户选择的模板或资源：").append(JsonUtils.toJSONString(context.getSelectedResources())).append("\n");
        }

        // 添加写作原则指导
        promptBuilder.append("\n请遵循以下写作原则：\n");
        promptBuilder.append("1. 内容应当专业、准确、符合行业标准\n");
        promptBuilder.append("2. 语言表达清晰流畅，结构合理\n");
        promptBuilder.append("3. 针对目标受众调整语言风格和专业程度\n");
        promptBuilder.append("4. 确保内容完整，覆盖用户需求的所有要点\n");
        promptBuilder.append("5. 维持适当的正式程度，符合文档类型要求\n\n");
        promptBuilder.append("请直接输出最终写作成果，无需解释你的写作过程。\n");

        Queue<String> contentBuffer = new ConcurrentLinkedQueue<>();
        AtomicBoolean done = new AtomicBoolean(false);

        // 调用大语言模型生成响应
//        Response response = editService.chatCompletionsStream(promptBuilder.toString(), "gpt-4o-0806");
        Response response = editService.chatCompletionsStream(null);

        // 创建响应流，定时检查并返回响应内容
        return Flux.interval(Duration.ofMillis(30))
                .onBackpressureBuffer(200)  // 设置背压缓冲区大小
                .take(Duration.ofMinutes(4))  // 最长响应时间4分钟
                .map(interval -> {
                    try {
                        // 读取并解析大语言模型的流式响应
                        BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().asInputStream()));
                        String line;
                        while ((line = reader.readLine()) != null) {
                            if (StringUtils.isEmpty(line)) continue;
                            if (line.contains("data: [DONE]")) {
                                done.set(true);
                                break;
                            }
                            if (line.startsWith("data: ")) {
                                line = line.substring(6);
                            }
                            ChatCompletionChunkDTO chunk = JsonUtils.parseObject(line, ChatCompletionChunkDTO.class);
                            if (chunk != null && CollectionUtils.isNotEmpty(chunk.getChoices())) {
                                for (ChatCompletionChunkDTO.Choice choice : chunk.getChoices()) {
                                    if (choice != null && choice.getDelta().getContent() != null) {
                                        // 封装为前端可识别的响应格式
                                        ChatCompletionChunkVO vo = new ChatCompletionChunkVO();
                                        vo.setNewContent(choice.getDelta().getContent());
                                        return JsonUtils.toJSONString(vo);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("生成响应出错", e);
                    }
                    return "";
                })
                .filter(Objects::nonNull)  // 过滤空响应
                .takeUntil(s -> done.get())  // 当响应完成时结束流
                .doOnTerminate(() -> log.info("响应生成完成"));
    }
} 