package com.jd.jdt.joycreator.ae.controller;

import com.jd.jdt.app4s.component.common.api.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.pojo.dto.AutofillExtractionDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.EditDocumentDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.ToolDtlDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.UserIntentDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.AutofillExtractionVO;
import com.jd.jdt.joycreator.ae.pojo.vo.EditDocumentVO;
import com.jd.jdt.joycreator.ae.service.EditDocumentService;
import com.jd.jdt.joycreator.ae.service.ChatStudioService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;


/**
 * <p>
 * 编辑器文档数据 服务接口
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-04-22
 */
@Log4j2
@RestController
@RequestMapping("/api/v1/edit-document")
public class EditDocumentController {

    @Autowired
    private EditDocumentService editDocumentService;
    @Autowired
    private ChatStudioService editService;


    @PostMapping("/save")
    public ResponseResult<Long> save(@RequestBody() EditDocumentDTO editDocumentDTO) {
        return ResponseResult.success(editDocumentService.save(editDocumentDTO));
    }

    @GetMapping("/dtl/{sessionId}")
    public ResponseResult<EditDocumentVO> dtl(@PathVariable("sessionId") String sessionId) {
        return ResponseResult.success(editDocumentService.dtl(sessionId));
    }

    /**
     * word转html
     *
     * @param fileId 附件ID
     * @return
     * @throws IOException
     */
    @GetMapping("/convert-html/{fileId}")
    public ResponseResult<String> convertHtml(@PathVariable("fileId") Long fileId) throws IOException {
        return ResponseResult.success("success", editDocumentService.convertHtml(fileId));
    }

    /**
     * 获取全部创作文档列表
     *
     * @return
     * @throws IOException
     */
    @GetMapping("/list")
    public ResponseResult<List<EditDocumentVO>> list() throws IOException {
        return ResponseResult.success(editDocumentService.list());
    }

    /**
     * 根据sessionId删除文档
     *
     * @return
     * @throws IOException
     */
    @DeleteMapping("/del/{sessionId}")
    public ResponseResult<Boolean> del(@PathVariable("sessionId") String sessionId) {
        return ResponseResult.success(editDocumentService.del(sessionId));
    }

    /**
     * 替换/改写抽取原文
     *
     * @param toolDtlDTO
     * @return
     */
    @PostMapping(value = "/autofill-extraction")
    public ResponseResult<AutofillExtractionVO> autofillExtraction(@RequestBody ToolDtlDTO toolDtlDTO) {
        return ResponseResult.success(editService.autofillExtraction(toolDtlDTO));
    }

    /**
     * 文档起草命名
     *
     * @param userIntent
     * @return
     */
    @PostMapping(value = "/create-theme")
    public ResponseResult<String> chatCreateTheme(@RequestBody UserIntentDTO userIntent) {
        return ResponseResult.success("success", editService.chatCreateTheme(userIntent));
    }

}
