package com.jd.jdt.joycreator.ae.dao.plus.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jd.jdt.joycreator.ae.entity.TemplateLibrary;
import com.jd.jdt.joycreator.ae.dao.mapper.TemplateLibraryMapper;
import com.jd.jdt.joycreator.ae.dao.plus.ITemplateLibraryMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IBaseMapperImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 模板库 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
public class ITemplateLibraryMapperImpl extends IBaseMapperImpl<TemplateLibraryMapper, TemplateLibrary> implements ITemplateLibraryMapper {

    @Override
    public List<TemplateLibrary> listTemplateLibraryByTag(String tag, String name) {
        LambdaQueryWrapper<TemplateLibrary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(TemplateLibrary::getId, TemplateLibrary::getTag, TemplateLibrary::getName, TemplateLibrary::getDescription);
        if (StringUtils.isNotBlank(tag)) {
            queryWrapper.like(TemplateLibrary::getTag, tag);
        }
        queryWrapper.like(TemplateLibrary::getName, name);
        return list(queryWrapper);
    }

    @Override
    public List<TemplateLibrary> listTemplateLibrary() {
        LambdaQueryWrapper<TemplateLibrary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(TemplateLibrary::getId, TemplateLibrary::getName, TemplateLibrary::getTag, TemplateLibrary::getDescription);
        return list(queryWrapper);
    }

    @Override
    public List<TemplateLibrary> listTemplateLibraryByIds(List<Long> ids) {
        LambdaQueryWrapper<TemplateLibrary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(TemplateLibrary::getId, TemplateLibrary::getTag, TemplateLibrary::getName, TemplateLibrary::getDescription);
        queryWrapper.in(TemplateLibrary::getId, ids);
        String idOrder = ids.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(", "));
        queryWrapper.last("ORDER BY FIELD(id, " + idOrder + ")");
        return list(queryWrapper);
    }

    @Override
    public List<TemplateLibrary> listTemplateLibraryByLimit(Long limit) {
        LambdaQueryWrapper<TemplateLibrary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(TemplateLibrary::getId, TemplateLibrary::getName);
        if (Objects.nonNull(limit)) {
            queryWrapper.last("LIMIT " + limit);
        }
        return list(queryWrapper);
    }
}
