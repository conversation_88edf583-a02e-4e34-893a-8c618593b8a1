package com.jd.jdt.joycreator.ae.util;

import com.aspose.words.Document;
import com.aspose.words.SaveFormat;
import java.io.File;
import java.io.IOException;

/**
 * 利用Aspose Words实现Word文档转换为PDF格式的工具类
 */
public class WordToPdfConverter {

    /**
     * 将Word文档转换为PDF格式
     *
     * @param inputWordPath 输入Word文件路径
     * @param outputPdfPath 输出PDF文件路径
     * @throws IOException 如果文件操作失败抛出异常
     */
    public static void convertToPdf(String inputWordPath, String outputPdfPath) throws Exception {
        System.out.println("开始PDF转换: 输入文件=" + inputWordPath + ", 输出文件=" + outputPdfPath);

        // 验证输入文件是否存在
        File inputFile = new File(inputWordPath);
        System.out.println("验证输入文件是否存在: " + inputFile.getAbsolutePath());
        if (!inputFile.exists()) {
            throw new IOException("输入文件不存在: " + inputWordPath);
        }
        System.out.println("输入文件大小: " + inputFile.length() + "字节");

        // 加载Word文档
        System.out.println("开始加载Word文档...");
        Document doc = new Document(inputWordPath);
        System.out.println("Word文档加载成功，页面数量: " + doc.getPageCount());

        // 保存为PDF
        System.out.println("开始保存PDF文件...");
        doc.save(outputPdfPath, SaveFormat.PDF);
        System.out.println("PDF文件保存成功");

        // 验证输出文件
        File outputFile = new File(outputPdfPath);
        if (outputFile.exists()) {
            System.out.println("输出PDF文件大小: " + outputFile.length() + "字节");
        } else {
            System.out.println("警告: 输出PDF文件未找到");
        }
    }


}