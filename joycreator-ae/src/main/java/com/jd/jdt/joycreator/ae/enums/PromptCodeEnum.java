package com.jd.jdt.joycreator.ae.enums;

/**
 * <p>
 * 提示词code枚举
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-05-20
 */
public enum PromptCodeEnum {

    CHAT_INTENT_RECOGNITION("CHAT_INTENT_RECOGNITION", "聊天快速意图识别提示词"),
    CHAT_CREATE_OUTLINE("CHAT_CREATE_OUTLINE", "大纲生成提示词"),
    CHAT_CREATE_PIECEWISE("CHAT_CREATE_PIECEWISE", "根据大纲写作提示词"),
    CHAT_USER_SEMANTIC_EXTRACTION("CHAT_USER_SEMANTIC_EXTRACTION", "用户会话记录语义快速识别提示词"),
    CHAT_REWRITE("CHAT_REWRITE", "根据原文片段改写文档内容提示词"),
    CHAT_TEXT_EXTRACTION("CHAT_TEXT_EXTRACTION", "原文提取提示词"),
    SUMMARIZE("SUMMARIZE", "缩写提示词"),
    EXPAND("EXPAND", "扩写提示词"),
    POLISH("POLISH", "润色提示词"),
    TEMPLATE_SIMILARITY_QUERIES("TEMPLATE_SIMILARITY_QUERIES", "模版相似度抽取提示词"),
    CHAT_CREATE_PIECEWISE_NEW("CHAT_CREATE_PIECEWISE_NEW", "分段撰写（根据大纲）提示词"),
    DOCUMENT_REPLACE("DOCUMENT_REPLACE", "根据关键词原文替换提示词"),
    AUTOFILL("AUTOFILL", "模版自动填充关键词列表获取提示词"),
    DOCUMENT_ANALYSIS("DOCUMENT_ANALYSIS", "文档模版类型与概述识别提示词"),
    CREATE_THEME("CREATE_THEME", "文件名称初始化提示词"),;


    /**
     * 源文件类型
     */
    private String Type;

    /**
     * 描述
     */
    private String desc;


    PromptCodeEnum(String type, String desc) {
        this.Type = type;
        this.desc = desc;
    }

    public String getType() {
        return Type;
    }

    public void setType(String type) {
        Type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
