package com.jd.jdt.joycreator.ae.utils;

import lombok.extern.log4j.Log4j2;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * XML解析工具类，用于从混杂内容中提取XML格式的数据
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Log4j2
public class XmlParserUtil {


    /**
     * 从混杂内容中提取模板库XML信息
     *
     * @param mixedContent 混杂内容，包含XML和其他字符串
     * @return 提取的模板库列表
     */
    public static List<Map<String, String>> extractTemplateLibraries(String mixedContent) {
        if (!StringUtils.hasText(mixedContent)) {
            return new ArrayList<>();
        }
        List<Map<String, String>> result = new ArrayList<>();
        Pattern pattern = Pattern.compile("<template_library>(.*?)</template_library>", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(mixedContent);
        while (matcher.find()) {
            String templateXml = matcher.group(0); // 完整的template_library XML片段
            Map<String, String> templateInfo = parseTemplateXml(templateXml, Arrays.asList("template_name", "template_type"));
            if (!templateInfo.isEmpty()) {
                result.add(templateInfo);
            }
        }

        return result;
    }


    public static List<Map<String, String>> extractDoccontentRewrite(String mixedContent) {
        if (!StringUtils.hasText(mixedContent)) {
            return new ArrayList<>();
        }
        List<Map<String, String>> result = new ArrayList<>();
        // 定义模板库XML的正则表达式模式
        Pattern pattern = Pattern.compile("<doccontentRewrite>(.*?)</doccontentRewrite>", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(mixedContent);
        while (matcher.find()) {
            String templateXml = matcher.group(0);
            Map<String, String> templateInfo = parseTemplateXml(templateXml, Arrays.asList("doccontent_rewrite_keyword", "doccontent_rewrite_style"));
            if (!templateInfo.isEmpty()) {
                result.add(templateInfo);
            }
        }

        return result;
    }


    public static List<Map<String, String>> extractFreelanceWriting(String mixedContent) {
        if (!StringUtils.hasText(mixedContent)) {
            return new ArrayList<>();
        }
        List<Map<String, String>> result = new ArrayList<>();
        // 定义模板库XML的正则表达式模式
        Pattern pattern = Pattern.compile("<freelanceWriting>(.*?)</freelanceWriting>", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(mixedContent);
        while (matcher.find()) {
            String templateXml = matcher.group(0);
            Map<String, String> templateInfo = parseTemplateXml(templateXml, Arrays.asList("freelance_writing_topic", "freelance_writing_requirements"));
            if (!templateInfo.isEmpty()) {
                result.add(templateInfo);
            }
        }

        return result;
    }

    /**
     * 从混杂内容中提取模板库XML信息
     *
     * @param mixedContent 混杂内容，包含XML和其他字符串
     * @return 提取的模板库列表
     */
    public static List<Map<String, String>> extractTextReplacement(String mixedContent) {
        if (!StringUtils.hasText(mixedContent)) {
            return new ArrayList<>();
        }
        List<Map<String, String>> result = new ArrayList<>();
        // 定义模板库XML的正则表达式模式
        Pattern pattern = Pattern.compile("<textReplacement>(.*?)</textReplacement>", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(mixedContent);
        while (matcher.find()) {
            String templateXml = matcher.group(0);
            Map<String, String> templateInfo = parseTemplateXml(templateXml, Arrays.asList("text_replacement_command"));
            if (!templateInfo.isEmpty()) {
                result.add(templateInfo);
            }
        }

        return result;
    }

    public static List<Map<String, String>> extractDoccontentExtraction(String mixedContent) {
        if (!StringUtils.hasText(mixedContent)) {
            return new ArrayList<>();
        }
        List<Map<String, String>> result = new ArrayList<>();
        // 定义模板库XML的正则表达式模式
        Pattern pattern = Pattern.compile("<doccontentExtraction>(.*?)</doccontentExtraction>", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(mixedContent);
        while (matcher.find()) {
            String templateXml = matcher.group(0);
            Map<String, String> templateInfo = parseTemplateXml(templateXml, Arrays.asList("doccontent_extraction_keyword"));
            if (!templateInfo.isEmpty()) {
                result.add(templateInfo);
            }
        }

        return result;
    }

    /**
     * 解析单个模板库XML片段
     *
     * @param templateXml 模板库XML片段
     * @return 解析后的模板信息Map
     */
    private static Map<String, String> parseTemplateXml(String templateXml, List<String> tagNameList) {
        Map<String, String> templateInfo = new HashMap<>();

        for (String tagName : tagNameList) {
            extractXmlTag(templateXml, tagName, templateInfo);
        }
        return templateInfo;
    }

    /**
     * 从XML中提取指定标签的内容
     *
     * @param xml       XML内容
     * @param tagName   标签名
     * @param resultMap 结果Map
     */
    private static void extractXmlTag(String xml, String tagName, Map<String, String> resultMap) {
        Pattern pattern = Pattern.compile("<" + tagName + ">(.*?)</" + tagName + ">", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(xml);

        if (matcher.find()) {
            String value = matcher.group(1).trim();
            resultMap.put(tagName, value);
        }
    }

    /**
     * 使用DOM解析器解析完整的XML内容（如果混杂内容可以被识别为有效的XML）
     *
     * @param content 可能包含完整XML的内容
     * @return 是否成功解析
     */
    public static boolean tryParseCompleteXml(String content) {
        try {
            // 尝试找到XML声明和根元素
            int startIndex = content.indexOf("<?xml");
            if (startIndex >= 0) {
                // 这里可以使用DOM解析器处理完整的XML
                // 但由于题目描述的是混杂内容，这里只是提供一个示例框架
                log.info("找到可能的完整XML内容，起始位置: {}", startIndex);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("尝试解析完整XML失败", e);
            return false;
        }
    }

    /**
     * 示例使用方法
     */
    public static void main(String[] args) {
        String ssss = "### 2.2 产品数量\n\n买卖双方已达成一致的产品数量如下：\n\n| 产品名称       | 型号         | 数量 | 单位  |\n|----------------|--------------|------|-------|\n| 智能温控器    | TC-2022      | 150  | 台    |\n| 工业传感器    | IS-5000      | 80   | 套    |";
        System.err.println(ssss);
        // 示例混杂内容
        String mixedContent = "好的，我们开始起草一份销售合同。首先，我们需要明确这份合同的基本结构和内容。一般来说，销售合同通常包括以下几个部分：\n" +
                "\n" +
                "合同双方信息：包括买方和卖方的名称、地址和联系方式。\n" +
                "合同标的：详细描述销售的商品或服务。\n" +
                "价格和支付条款：明确商品或服务的价格，以及支付的方式和时间。\n" +
                "交货条款：规定交货时间、地点和方式。\n" +
                "质量保证和售后服务：明确商品或服务的质量标准和售后服务条款。\n" +
                "违约责任：规定双方在违约情况下的责任和赔偿。\n" +
                "争议解决：明确争议解决方式，如仲裁或诉讼。\n" +
                "其他条款：包括合同的生效日期、修改条款等。\n" +
                "接下来，我将使用模板工具来获取一个销售合同的模板，以确保我们有一个专业的起点。\n" +
                "\n" +
                "<template_library>\n" +
                "<template_name>销售合同模版</template_name>\n" +
                "<template_type>合同</template_type>\n" +
                "</template_library>";

        List<Map<String, String>> templates = extractTemplateLibraries(mixedContent);
        for (Map<String, String> template : templates) {
            System.out.println("模板名称: " + template.get("template_name"));
            System.out.println("模板类型: " + template.get("template_type"));
            System.out.println("-------------------");
        }
    }
}
