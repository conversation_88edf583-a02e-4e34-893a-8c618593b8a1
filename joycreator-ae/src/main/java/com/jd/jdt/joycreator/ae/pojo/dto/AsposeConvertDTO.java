package com.jd.jdt.joycreator.ae.pojo.dto;

import com.jd.jdt.joycreator.ae.enums.AsposeConvertTypeEnum;
import lombok.Data;

/**
 * <p>
 * AsposeConvertDTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
public class AsposeConvertDTO {

    /**
     * 文件名称（带后缀）
     */
    private String fileName;


    /**
     * 文件字节数组
     */
    private byte[] byteArray;

    /**
     * 转换类型
     */
    private AsposeConvertTypeEnum asposeConvertType;
} 