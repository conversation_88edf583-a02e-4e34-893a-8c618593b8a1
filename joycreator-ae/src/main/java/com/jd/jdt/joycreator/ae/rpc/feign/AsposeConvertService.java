package com.jd.jdt.joycreator.ae.rpc.feign;

import com.jd.jdt.joybuilder.permission.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.config.FeignConfig;
import com.jd.jdt.joycreator.ae.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.AsposeConvertVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * Aspose文档转换服务Feign客户端
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@FeignClient(name = "llm-gateway-service", url = "${rpc.aspose.url}", configuration = FeignConfig.class)
public interface AsposeConvertService {

    /**
     * Office文档转换接口
     *
     * @param asposeConvertDTO 转换请求参数
     * @return 转换结果
     */
    @PostMapping(value = "/api/v1/aspose-convert/word", consumes = "application/json")
    ResponseResult<AsposeConvertVO> asposeConvert(@RequestBody AsposeConvertDTO asposeConvertDTO);
}