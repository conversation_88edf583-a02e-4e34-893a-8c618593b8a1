package com.jd.jdt.joycreator.ae.service.impl;

import com.google.common.collect.Maps;
import com.jd.jdt.app4s.component.common.api.enums.ResultCode;
import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jdt.joybuilder.permission.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.dao.plus.IChatHistoryDetailMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IChatHistoryMapper;
import com.jd.jdt.joycreator.ae.entity.ChatHistory;
import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetail;
import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetailExcerpt;
import com.jd.jdt.joycreator.ae.entity.PromptWordsConfig;
import com.jd.jdt.joycreator.ae.enums.*;
import com.jd.jdt.joycreator.ae.pojo.dto.*;
import com.jd.jdt.joycreator.ae.pojo.vo.*;
import com.jd.jdt.joycreator.ae.rpc.feign.LLMGatewayService;
import com.jd.jdt.joycreator.ae.service.*;
import com.jd.jdt.joycreator.ae.utils.*;
import com.jd.jsf.gd.util.JsonUtils;
import feign.Response;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 * <p>
 * 大模型网关接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@Log4j2
@Service
public class ChatStudioServiceImpl implements ChatStudioService {

    @Autowired
    private LLMGatewayService llmGatewayService;
    @Autowired
    private TemplateLibraryService templateLibraryService;
    @Autowired
    private ChatHistoryDetailService chatHistoryDetailService;
    @Autowired
    private IChatHistoryMapper iChatHistoryMapper;
    @Autowired
    private MarkdownParsingUtils markdownParsingUtils;
    @Autowired
    private EditDocumentService editDocumentService;
    @Autowired
    private PromptWordsConfigService promptWordsConfigService;
    @Autowired
    private JoyCreatorUtil joyCreatorUtil;
    @Autowired
    private IChatHistoryDetailMapper iChatHistoryDetailMapper;

    @Value("${rpc.llm.apiKey}")
    private String apiKey;
    @Value("${rpc.llm.jdcloudPin}")
    private String jdcloudPin;


    @Override
    public Response chatCompletionsStream(PromptWordsConfig promptWordsConfig) {
        Map<String, Object> params = new HashMap<>();
        params.put("stream", Boolean.TRUE);
        params.put("model", promptWordsConfig.getModel());
        List<Object> messages = Lists.newArrayList();
        Map<String, String> map = Maps.newHashMap();
        map.put("role", "system");
        map.put("content", promptWordsConfig.getPromptWord());
        messages.add(map);
        params.put("messages", messages);
        if (Objects.nonNull(promptWordsConfig.getMaxTokens())) {
            params.put("max_tokens", promptWordsConfig.getMaxTokens().longValue());
        }
        if (Objects.nonNull(promptWordsConfig.getTemperature()) || Objects.nonNull(promptWordsConfig.getTopK())) {
            params.put("do_sample", Boolean.TRUE);
            if (Objects.nonNull(promptWordsConfig.getTemperature())) {
                params.put("temperature", promptWordsConfig.getTemperature().floatValue());
            }
            if (Objects.nonNull(promptWordsConfig.getTopK())) {
                params.put("top_p", promptWordsConfig.getTopK().floatValue());
            }
        }
        log.info("聊天完成接口/流式，apiKey：{}，params：{}", apiKey, JsonUtils.toJSONString(params));
        return llmGatewayService.chatCompletionsStream(apiKey, params);
    }

    @Override
    public ChatCompletionResponseDTO chatCompletions(PromptWordsConfig promptWordsConfig) {
        Map<String, Object> params = new HashMap<>();
        params.put("model", promptWordsConfig.getModel());
        List<Object> messages = Lists.newArrayList();
        Map<String, String> map = Maps.newHashMap();
        map.put("role", "system");
        map.put("content", promptWordsConfig.getPromptWord());
        messages.add(map);
        params.put("messages", messages);
        if (Objects.nonNull(promptWordsConfig.getMaxTokens())) {
            params.put("max_tokens", promptWordsConfig.getMaxTokens().longValue());
        }
        if (Objects.nonNull(promptWordsConfig.getTemperature()) || Objects.nonNull(promptWordsConfig.getTopK())) {
            params.put("do_sample", Boolean.TRUE);
            if (Objects.nonNull(promptWordsConfig.getTemperature())) {
                params.put("temperature", promptWordsConfig.getTemperature().floatValue());
            }
            if (Objects.nonNull(promptWordsConfig.getTopK())) {
                params.put("top_p", promptWordsConfig.getTopK().floatValue());
            }
        }
        log.info("聊天完成接口/非流式，apiKey：{}，params：{}", apiKey, JsonUtils.toJSONString(params));
        ChatCompletionResponseDTO chatCompletionResponseDTO = llmGatewayService.chatCompletions(apiKey, params);
        log.info("聊天完成接口/非流式，chatCompletionResponseDTO：{}", JsonUtils.toJSONString(chatCompletionResponseDTO));
        return chatCompletionResponseDTO;
    }

    @Override
    public ChatCompletionResponseDTO chatCompletions(PromptWordsConfigDTO promptWordsConfigDTO) {
        Map<String, Object> params = new HashMap<>();
        params.put("model", promptWordsConfigDTO.getModel());
        List<Object> messages = Lists.newArrayList();
        Map<String, String> map = Maps.newHashMap();
        map.put("role", "system");
        map.put("content", promptWordsConfigDTO.getPromptWord());
        messages.add(map);
        if (Objects.nonNull(promptWordsConfigDTO.getMaxTokens())) {
            params.put("max_tokens", promptWordsConfigDTO.getMaxTokens().longValue());
        }
        if (Objects.nonNull(promptWordsConfigDTO.getTemperature()) || Objects.nonNull(promptWordsConfigDTO.getTopK())) {
            params.put("do_sample", Boolean.TRUE);
            if (Objects.nonNull(promptWordsConfigDTO.getTemperature())) {
                params.put("temperature", promptWordsConfigDTO.getTemperature().floatValue());
            }
            if (Objects.nonNull(promptWordsConfigDTO.getTopK())) {
                params.put("top_p", promptWordsConfigDTO.getTopK().floatValue());
            }
        }
        List<Map<String, String>> userChatMesList = promptWordsConfigDTO.getDoronChatMessageDetailDTOList().stream().
                filter(d -> (d.getChatRole() == ChatRoleEnum.USER || d.getChatRole() == ChatRoleEnum.TEXT || d.getChatRole() == ChatRoleEnum.ASSISTANT || d.getChatRole() == ChatRoleEnum.TASK_FEEDBACK) && StringUtils.isNotBlank(d.getChatContent())).map(d -> {
                    Map<String, String> mapUser = Maps.newHashMap();
                    if (d.getChatRole() == ChatRoleEnum.TEXT || d.getChatRole() == ChatRoleEnum.USER) {
                        mapUser.put("role", "user");
                        mapUser.put("content", d.getChatContent());
                    } else {
                        mapUser.put("role", "assistant");
                        mapUser.put("content", d.getChatContent());
                    }
                    return mapUser;
                }).collect(Collectors.toList());
        messages.addAll(userChatMesList);
        params.put("messages", messages);
        log.info("聊天完成接口/非流式，apiKey：{}，params：{}", apiKey, JsonUtils.toJSONString(params));
        ChatCompletionResponseDTO chatCompletionResponseDTO = llmGatewayService.chatCompletions(apiKey, params);
        log.info("聊天完成接口/非流式，chatCompletionResponseDTO：{}", JsonUtils.toJSONString(chatCompletionResponseDTO));
        return chatCompletionResponseDTO;
    }

    @Override
    public Object doronChat(DoronChatDTO doronChatDTO) {
        Boolean stream = doronChatDTO.getStream();
        log.info("聊天完成接口/流式-多伦会话，doronChatDTO：{}", JsonUtils.toJSONString(doronChatDTO));
        this.doronChatCheck(doronChatDTO);
        //根据用户chat上下文分析用户语义
        StringBuffer messagesStr = new StringBuffer();
        for (DoronChatMessageDTO message : doronChatDTO.getMessages()) {
            if (CollectionUtils.isEmpty(message.getMessageDetailList())) {
                continue;
            }
            ChatRoleEnum chatRole = message.getChatRole();
            int level = message.getLevel();
            for (DoronChatMessageDetailDTO messageDetail : message.getMessageDetailList()) {
                if (StringUtils.isEmpty(messageDetail.getChatContent())) {
                    continue;
                }
                if (ChatRoleEnum.USER == chatRole && messageDetail.getChatRole() == ChatRoleEnum.TEXT) {
                    messagesStr.append(ChatRoleEnum.USER.getType()).append("(level ").append(level).append("): ").append(messageDetail.getChatContent()).append("\n");
                } else if (ChatRoleEnum.ASSISTANT == chatRole && (messageDetail.getChatRole() == ChatRoleEnum.ASSISTANT || messageDetail.getChatRole() == ChatRoleEnum.TASK_FEEDBACK)) {
                    messagesStr.append(ChatRoleEnum.ASSISTANT.getType()).append("(level ").append(level).append("): ").append(messageDetail.getChatContent()).append("\n");
                }
            }
        }
        log.info("聊天完成接口/流式-多伦会话，messagesStr：{}", messagesStr);
        iChatHistoryMapper.create(doronChatDTO.getSessionId());
        Queue<Object> contentBuffer = new ConcurrentLinkedQueue<>();
//        String userSemantic = this.getUserSemantic(doronChatDTO.getMessages());
        String sessionId = doronChatDTO.getSessionId();
        Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue = new ConcurrentLinkedQueue<>();
        AtomicBoolean done = new AtomicBoolean(false);
        this.addChatHistoryDetailDTOQueue(doronChatDTO, chatHistoryDetailDTOQueue);
        List<DoronChatMessageDetailDTO> doronChatMessageDetailDTOList = doronChatDTO.getMessages().stream().map(DoronChatMessageDTO::getMessageDetailList).flatMap(List::stream).collect(Collectors.toList());
        List<ToolDtlDTO> toolDtlDTOList = this.addContentBuffer(contentBuffer, chatHistoryDetailDTOQueue, doronChatMessageDetailDTOList, stream);
        if (CollectionUtils.isNotEmpty(toolDtlDTOList)) {
            this.callTools(sessionId, contentBuffer, chatHistoryDetailDTOQueue, doronChatMessageDetailDTOList, toolDtlDTOList, done, stream, messagesStr.toString());
        } else {
            done.set(Boolean.TRUE);
        }
        chatHistoryDetailService.saveBatch(chatHistoryDetailDTOQueue, done, sessionId);
        if (stream) {
            return this.buildFlux(contentBuffer, done);
        }

        long startTime = System.currentTimeMillis();
        while (!done.get()) {
            long timeout = 60000;
            if (System.currentTimeMillis() - startTime > timeout) {
                log.warn("聊天完成接口/非流式-多伦会话，执行超时，done：{}", done.get());
                return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(ResponseResult.error(ResultCode.SERVICE_TIMEOUT));
            }
            try {
                Thread.sleep(60);
            } catch (InterruptedException e) {
            }
        }
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(ResponseResult.success("响应成功", contentBuffer));
    }

    private List<String> listUserMsg(List<DoronChatMessageDTO> messages) {
        return messages.stream().filter(m -> m.getChatRole() == ChatRoleEnum.USER).map(d -> d.getMessageDetailList().stream().filter(d2 -> d2.getChatRole() == ChatRoleEnum.TEXT).map(DoronChatMessageDetailDTO::getChatContent).collect(Collectors.joining())).collect(Collectors.toList());
    }

    private List<ToolDtlDTO> addContentBuffer(Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue, List<DoronChatMessageDetailDTO> doronChatMessageDetailDTOList, Boolean stream) {
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CHAT_INTENT_RECOGNITION);
        PromptWordsConfigDTO promptWordsConfigDTO = new PromptWordsConfigDTO();
        BeanUtils.copyProperties(promptWordsConfig, promptWordsConfigDTO);
        promptWordsConfigDTO.setDoronChatMessageDetailDTOList(doronChatMessageDetailDTOList);
        ChatCompletionResponseDTO chatCompletionResponseDTO = this.chatCompletions(promptWordsConfigDTO);
        String content = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
        if (StringUtils.isEmpty(content)) {
            ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
            chatCompletionChunkVO.setNewContent(content);
            chatCompletionChunkVO.setChatRole(ChatRoleEnum.ASSISTANT);
            contentBuffer.add(chatCompletionChunkVO);
            return null;
        }

        String contentNew = content;
        List<ToolDtlDTO> toolDtlDTOList = ToolParseUtil.parseTools(content);
        List<ToolLabelsEnum> toolLabelsList = Arrays.stream(ToolLabelsEnum.values()).filter(t -> content.contains(t.getType())).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(toolLabelsList)) {
            Pattern pattern = Pattern.compile(toolLabelsList.get(0).getType() + ".*", Pattern.DOTALL);
            contentNew = pattern.matcher(contentNew).replaceAll("");
        }
        if (CollectionUtils.isEmpty(toolLabelsList)) {
            String businessNo = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
            if (stream) {
                List<String> contentNewList = RandomStringSplitter.splitIntoRandomGroups(contentNew);
//            char[] charArray = contentNew.toCharArray();
                for (int i = 0; i < contentNewList.size(); i++) {
                    ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
                    chatCompletionChunkVO.setNewContent(contentNewList.get(i));
                    chatCompletionChunkVO.setChatRole(ChatRoleEnum.ASSISTANT);
                    chatCompletionChunkVO.setBusinessNo(businessNo);
                    contentBuffer.add(chatCompletionChunkVO);
                    if (contentNewList.size() == i + 1) {

                        ChatHistoryDetailDTO chatHistoryDetailDTO = new ChatHistoryDetailDTO();
                        chatHistoryDetailDTO.setChatRole(ChatRoleEnum.ASSISTANT);
                        chatHistoryDetailDTO.setChatContent(contentNew);
                        chatHistoryDetailDTO.setBusinessNo(businessNo);
                        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO);
                    }
                }
            } else {
                ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
                chatCompletionChunkVO.setNewContent(contentNew);
                chatCompletionChunkVO.setChatRole(ChatRoleEnum.ASSISTANT);
                chatCompletionChunkVO.setBusinessNo(businessNo);
                contentBuffer.add(chatCompletionChunkVO);
                ChatHistoryDetailDTO chatHistoryDetailDTO = new ChatHistoryDetailDTO();
                chatHistoryDetailDTO.setChatRole(ChatRoleEnum.ASSISTANT);
                chatHistoryDetailDTO.setChatContent(contentNew);
                chatHistoryDetailDTO.setBusinessNo(businessNo);
                chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO);
            }
            return toolDtlDTOList;
        }
        List<ToolDtlDTO> coToolDtlDTOFreelanceWriting = toolDtlDTOList.stream().filter(t -> t.getToolLabels() == ToolLabelsEnum.FREELANCE_WRITING).distinct().collect(Collectors.toList());

        String toolname = ChatRoleEnum.FREELANCE_WRITING_START.getDesc();
        if (CollectionUtils.isEmpty(coToolDtlDTOFreelanceWriting)) {
            toolname = ChatRoleEnum.AUTOFILL.getDesc();
        }
        String businessNo = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
        ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
        chatCompletionChunkVO.setNewContent(toolname);
        chatCompletionChunkVO.setChatRole(ChatRoleEnum.TOOL);
        chatCompletionChunkVO.setBusinessNo(businessNo);
        contentBuffer.add(chatCompletionChunkVO);
        ChatHistoryDetailDTO chatHistoryDetailDTO = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO.setChatRole(ChatRoleEnum.TOOL);
        chatHistoryDetailDTO.setChatContent(toolname);
        chatHistoryDetailDTO.setBusinessNo(businessNo);
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO);

        return toolDtlDTOList;
    }

    @Override
    public Flux<String> chatCreate(DocumentWritingDTO documentWritingDTO) {
        log.info("聊天完成接口/流式，documentWritingDTO：{}", JsonUtils.toJSONString(documentWritingDTO));
        this.chatCreateCheck(documentWritingDTO);
        Queue<Object> contentBuffer = new ConcurrentLinkedQueue<>();
        AtomicBoolean done = new AtomicBoolean(false);
        this.buildChatCreateBuffer(documentWritingDTO, contentBuffer, done);
        return this.buildFlux(contentBuffer, done);
    }

    @Override
    public Flux<String> processText(TextProcessingDTO textProcessingDTO) {
        log.info("文本处理接口/流式，textProcessingDTO：{}", JsonUtils.toJSONString(textProcessingDTO));
        String convertHtmlToMarkdown = markdownParsingUtils.convertHtmlToMarkdown(textProcessingDTO.getContent());
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(textProcessingDTO.getOperationType(), convertHtmlToMarkdown);
//        String prompts = textProcessingDTO.getOperationType().getPromptTemplate().replace("${EXPANDED_WORDS}", textProcessingDTO.getContent());
        Queue<Object> contentBuffer = new ConcurrentLinkedQueue<>();
        AtomicBoolean done = new AtomicBoolean(false);
        this.buildContentBuffer(promptWordsConfig, contentBuffer, done);
        return this.buildFlux(contentBuffer, done);
    }

    @Override
    public Flux<String> chatRewrite(ChatRewriteDocumentDTO chatRewriteDocumentDTO) {
        log.info("改写/流式，chatRewriteDocumentDTO：{}", JsonUtils.toJSONString(chatRewriteDocumentDTO));
        String sessionId = chatRewriteDocumentDTO.getSessionId();
        String doccontentRewrite = chatRewriteDocumentDTO.getDocExtractionKeyword();
        Map<String, String> toolParams = chatRewriteDocumentDTO.getToolParams();
        String doccontentRewriteStyle = toolParams.get("doccontentRewriteStyle");
        Queue<Object> contentBuffer = new ConcurrentLinkedQueue<>();
        AtomicBoolean done = new AtomicBoolean(false);
        String originalMd = markdownParsingUtils.convertHtmlToMarkdown(doccontentRewrite);
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CHAT_REWRITE, doccontentRewriteStyle, originalMd);
        this.buildContentChatRewriteBuffer(promptWordsConfig, contentBuffer, sessionId, done);
        return this.buildFlux(contentBuffer, done);
    }

    private void buildContentChatRewriteBuffer(PromptWordsConfig promptWordsConfig, Queue<Object> contentBuffer, String sessionId, AtomicBoolean done) {
        new Thread(() -> {
            String businessNo = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
            String businessNo2 = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
            ChatCompletionResponseDTO chatCompletionResponseDTO = this.chatCompletions(promptWordsConfig);
            String content = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
            ChatRewriteDTO chatRewriteDTO = JsonIntentListParserUtils.parseObject(content, ChatRewriteDTO.class);
            if (Objects.isNull(chatRewriteDTO)) {
                done.set(Boolean.TRUE);
                return;
            }
            ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
            chatCompletionChunkVO.setBusinessNo(businessNo);
            chatCompletionChunkVO.setNewContent(chatRewriteDTO.getAfterDocumentContent());
            chatCompletionChunkVO.setChatRole(ChatRoleEnum.TEXT_REWRITE);
            contentBuffer.add(chatCompletionChunkVO);
            ChatCompletionChunkVO chatCompletionChunkVO2 = new ChatCompletionChunkVO();
            chatCompletionChunkVO2.setBusinessNo(businessNo2);
            chatCompletionChunkVO2.setNewContent(chatRewriteDTO.getTaskFeedback());
            chatCompletionChunkVO2.setChatRole(ChatRoleEnum.TASK_FEEDBACK);
            contentBuffer.add(chatCompletionChunkVO2);
            done.set(Boolean.TRUE);

            ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(sessionId);
            ChatHistoryDetail chatHistoryDetail = new ChatHistoryDetail();
            chatHistoryDetail.setChatHistoryId(chatHistory.getId());
            chatHistoryDetail.setChatRole(ChatRoleEnum.TEXT_REWRITE);
            chatHistoryDetail.setChatContent(chatRewriteDTO.getAfterDocumentContent());
            chatHistoryDetail.setBusinessNo(businessNo);
            ChatHistoryDetail chatHistoryDetail2 = new ChatHistoryDetail();
            chatHistoryDetail2.setChatHistoryId(chatHistory.getId());
            chatHistoryDetail2.setChatRole(ChatRoleEnum.TASK_FEEDBACK);
            chatHistoryDetail2.setChatContent(chatRewriteDTO.getTaskFeedback());
            chatHistoryDetail2.setBusinessNo(businessNo2);
            List<ChatHistoryDetail> chatHistoryDetails = Lists.newArrayList();
            chatHistoryDetails.add(chatHistoryDetail);
            chatHistoryDetails.add(chatHistoryDetail2);
            iChatHistoryDetailMapper.saveBatch(chatHistoryDetails);
        }).start();
    }

    @Override
    public List<AutofillVO> autofill(AutofillDTO autofillDTO) {
        log.info("模版自动填充关键列表抽取,autofillDTO：{}", JsonUtils.toJSONString(autofillDTO));
        if (Objects.isNull(autofillDTO)) {
            throw new BussinessBizException("历史会话记录为空!");
        }
        //根据用户chat上下文分析用户语义
        StringBuffer messagesStr = new StringBuffer();
        for (DoronChatMessageDTO message : autofillDTO.getMessages()) {
            ChatRoleEnum chatRole = message.getChatRole();
            int level = message.getLevel();
            for (DoronChatMessageDetailDTO messageDetail : message.getMessageDetailList()) {
                if (ChatRoleEnum.USER == chatRole && messageDetail.getChatRole() == ChatRoleEnum.TEXT) {
                    messagesStr.append(ChatRoleEnum.USER.getType()).append("(level ").append(level).append("): ").append(messageDetail.getChatContent()).append("\n");
                } else if (ChatRoleEnum.ASSISTANT == chatRole && (messageDetail.getChatRole() == ChatRoleEnum.ASSISTANT || messageDetail.getChatRole() == ChatRoleEnum.TASK_FEEDBACK)) {
                    messagesStr.append(ChatRoleEnum.ASSISTANT.getType()).append("(level ").append(level).append("): ").append(messageDetail.getChatContent()).append("\n");
                }
            }
        }
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.AUTOFILL, messagesStr.toString());
        ChatCompletionResponseDTO chatCompletionResponseDTO = this.chatCompletions(promptWordsConfig);
        String content = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
        List<AutofillVO> autofillVOList = JsonIntentListParserUtils.parseList(content, AutofillVO.class);
        log.info("模版自动填充关键列表抽取,autofillVOList:{}", JsonUtils.toJSONString(autofillVOList));
        //历史记录保存
        ChatHistoryDetail chatHistoryDetail = new ChatHistoryDetail();
        String sessionId = autofillDTO.getSessionId();
        ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(sessionId);
        chatHistoryDetail.setChatHistoryId(chatHistory.getId());
        chatHistoryDetail.setBusinessNo(joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX));
        chatHistoryDetail.setChatRole(ChatRoleEnum.TEXT_REWRITE);
        chatHistoryDetail.setChatContent(JsonUtils.toJSONString(autofillVOList));
        iChatHistoryDetailMapper.save(chatHistoryDetail);
        return autofillVOList;
    }

    @Override
    public AutofillExtractionVO autofillExtraction(ToolDtlDTO toolDtlDTO) {
        AutofillExtractionVO autofillExtractionVO = editDocumentService.autofillExtraction(toolDtlDTO);
        if (Objects.isNull(autofillExtractionVO)) {
            return null;
        }
        //历史记录保存
        List<ChatHistoryDetail> chatHistoryDetailList = Lists.newArrayList();
        String businessNo = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
        ChatHistoryDetail chatHistoryDetail = new ChatHistoryDetail();
        String sessionId = toolDtlDTO.getSessionId();
        ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(sessionId);
        chatHistoryDetail.setChatHistoryId(chatHistory.getId());
        chatHistoryDetail.setBusinessNo(businessNo);
        if (StringUtils.equals(ChatRoleEnum.TEXT_REPLACEMENT.getType(), toolDtlDTO.getToolLabels().name())) {
            chatHistoryDetail.setChatContent(JsonUtils.toJSONString(autofillExtractionVO));
            chatHistoryDetail.setChatRole(ChatRoleEnum.TEXT_REPLACEMENT);
        } else if (StringUtils.equals(ChatRoleEnum.DOCCONTENT_REWRITE.getType(), toolDtlDTO.getToolLabels().name())) {
            chatHistoryDetail.setChatContent(autofillExtractionVO.getOriginalParagraph());
            chatHistoryDetail.setChatRole(ChatRoleEnum.DOCCONTENT_EXTRACTION);
        }
        chatHistoryDetailList.add(chatHistoryDetail);
        if (toolDtlDTO.isLast()) {
            String businessNo2 = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
            ChatHistoryDetail chatHistoryDetail2 = new ChatHistoryDetail();
            chatHistoryDetail2.setChatHistoryId(chatHistory.getId());
            chatHistoryDetail2.setBusinessNo(businessNo2);
            chatHistoryDetail2.setChatRole(ChatRoleEnum.getEnum(ChatRoleEnum.TASK_FEEDBACK.getType()));
            chatHistoryDetail2.setChatContent("已帮您完成所有模版自动填充任务!");
            chatHistoryDetailList.add(chatHistoryDetail2);
            autofillExtractionVO.setTaskFeedback("✅已帮您完成所有模版自动填充任务!");
        }
        iChatHistoryDetailMapper.saveBatch(chatHistoryDetailList);
        return autofillExtractionVO;
    }

    @Override
    public String chatCreateTheme(UserIntentDTO userIntentDTO) {
        log.info("文档起草命名/非流式，userIntentDTO：{}", JsonUtils.toJSONString(userIntentDTO));
        if (Objects.isNull(userIntentDTO) || StringUtils.isEmpty(userIntentDTO.getUserIntent())) {
            throw new BussinessBizException("销售不能为空!");
        }

        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CREATE_THEME, userIntentDTO.getUserIntent());
        ChatCompletionResponseDTO chatCompletionResponseDTO = this.chatCompletions(promptWordsConfig);
        if (Objects.isNull(chatCompletionResponseDTO) || CollectionUtils.isEmpty(chatCompletionResponseDTO.getChoices()) ||
                Objects.isNull(chatCompletionResponseDTO.getChoices().get(0).getMessage()) ||
                StringUtils.isEmpty(chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent())) {
            return null;
        }
        String theme = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
        log.info("文档起草命名/非流式，theme：{}", theme);
        return theme;
    }


    private void buildChatCreateBuffer(DocumentWritingDTO documentWritingDTO, Queue<Object> contentBuffer, AtomicBoolean done) {
        StringBuffer contentBufferAll = new StringBuffer();
        log.info("聊天完成接口/非流式，文档大纲撰写，contentLLM（大纲）：{}", JsonUtils.toJSONString(documentWritingDTO));
        new Thread(() -> {
            String userSemantic = this.getUserSemantic(documentWritingDTO.getMessages());
            log.info("聊天完成接口/非流式，文档大纲撰写，userSemantic：{}", userSemantic);
//            Object toolsParam = documentWritingDTO.getToolsParam();
            CreateOutlineDTO createOutlineDTO = documentWritingDTO.getCreateOutlineDTO();
            List<CreateOutlineDTO> createOutlineList = createOutlineDTO.getCreateOutlineList();
            String title = createOutlineDTO.getTitle();
            ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
            chatCompletionChunkVO.setNewContent("# " + title + "\n");
            chatCompletionChunkVO.setChatRole(ChatRoleEnum.ASSISTANT);
            contentBuffer.add(chatCompletionChunkVO);
            contentBufferAll.append("# " + title + "\n");
            for (CreateOutlineDTO createOutlineDTONew : createOutlineList) {
                List<CreateOutlineDTO> createOutlineNewList = createOutlineDTONew.getCreateOutlineList();
                if (CollectionUtils.isEmpty(createOutlineNewList)) {
                    createOutlineDTONew.setCreateOutlineList(null);
                    PromptWordsConfig ChatCreatePiecewisePromptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CHAT_CREATE_PIECEWISE_NEW, title, userSemantic, JsonUtils.toJSONString(createOutlineDTONew));
                    this.buildChatCreatePiecewiseBuffer(ChatCreatePiecewisePromptWordsConfig, contentBuffer, contentBufferAll);
                } else {
                    String title1 = createOutlineDTONew.getTitle();
                    ChatCompletionChunkVO chatCompletionChunkVO2 = new ChatCompletionChunkVO();
                    chatCompletionChunkVO2.setNewContent("## " + title1 + "\n");
                    chatCompletionChunkVO2.setChatRole(ChatRoleEnum.ASSISTANT);
                    contentBuffer.add(chatCompletionChunkVO2);
                    contentBufferAll.append("## " + title1 + "\n");
//                    String theme = title + JsonUtils.toJSONString(createOutlineDTONew);
                    createOutlineNewList = createOutlineNewList.stream().map(c -> {
                        if (CollectionUtils.isEmpty(c.getCreateOutlineList())) {
                            c.setCreateOutlineList(null);
                        }
                        return c;
                    }).collect(Collectors.toList());
                    PromptWordsConfig ChatCreatePiecewisePromptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CHAT_CREATE_PIECEWISE_NEW, title, userSemantic, JsonUtils.toJSONString(createOutlineNewList));
                    this.buildChatCreatePiecewiseBuffer(ChatCreatePiecewisePromptWordsConfig, contentBuffer, contentBufferAll);
                }
            }

            ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(documentWritingDTO.getSessionId());
            String businessNo = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
            String taskFeedback = "已为您完成" + title + "的文档撰写!";
            ChatCompletionChunkVO chatCompletionChunkVO3 = new ChatCompletionChunkVO();
            chatCompletionChunkVO3.setBusinessNo(businessNo);
            chatCompletionChunkVO3.setNewContent(taskFeedback);
            chatCompletionChunkVO3.setChatRole(ChatRoleEnum.TASK_FEEDBACK);
            contentBuffer.add(chatCompletionChunkVO3);

            ChatHistoryDetail chatHistoryDetail = new ChatHistoryDetail();
            chatHistoryDetail.setChatHistoryId(chatHistory.getId());
            chatHistoryDetail.setChatRole(ChatRoleEnum.TASK_FEEDBACK);
            chatHistoryDetail.setChatContent(taskFeedback);
            chatHistoryDetail.setBusinessNo(businessNo);
            iChatHistoryDetailMapper.save(chatHistoryDetail);

            log.info("聊天完成接口/非流式，文档大纲撰写，userSemantic：{}，contentLLM（正文）：{}", userSemantic, contentBufferAll);

            done.set(Boolean.TRUE);
        }).start();
    }

    private void buildChatCreateBuffer(CreateOutlineVO createOutlineVO, Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue, String userSemantic) {
        String businessNo = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
        String businessNo2 = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
        StringBuffer contentBufferAll = new StringBuffer();
        log.info("聊天完成接口/非流式，文档大纲撰写，userSemantic：{}，contentLLM（大纲）：{}", userSemantic, JsonUtils.toJSONString(createOutlineVO));
//            Object toolsParam = documentWritingDTO.getToolsParam();
        List<CreateOutlineVO> createOutlineList = createOutlineVO.getCreateOutlineList();
        String title = createOutlineVO.getTitle();
        ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
        chatCompletionChunkVO.setBusinessNo(businessNo);
        chatCompletionChunkVO.setNewContent("# " + title + "\n");
        chatCompletionChunkVO.setChatRole(ChatRoleEnum.DOCUMENT);
        contentBuffer.add(chatCompletionChunkVO);
        contentBufferAll.append("# " + title + "\n");
        for (CreateOutlineVO createOutlineDTONew : createOutlineList) {
            List<CreateOutlineVO> createOutlineNewList = createOutlineDTONew.getCreateOutlineList();
            if (CollectionUtils.isEmpty(createOutlineNewList)) {
                createOutlineDTONew.setCreateOutlineList(null);
                PromptWordsConfig ChatCreatePiecewisePromptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CHAT_CREATE_PIECEWISE_NEW, title, userSemantic, JsonUtils.toJSONString(createOutlineDTONew));
                this.buildChatCreateBuffer(ChatCreatePiecewisePromptWordsConfig, contentBuffer, contentBufferAll, businessNo);
            } else {
                String title1 = createOutlineDTONew.getTitle();
                ChatCompletionChunkVO chatCompletionChunkVO2 = new ChatCompletionChunkVO();
                chatCompletionChunkVO2.setNewContent("## " + title1 + "\n");
                chatCompletionChunkVO2.setChatRole(ChatRoleEnum.DOCUMENT);
                chatCompletionChunkVO2.setBusinessNo(businessNo);
                contentBuffer.add(chatCompletionChunkVO2);
                contentBufferAll.append("## " + title1 + "\n");
//                    String theme = title + JsonUtils.toJSONString(createOutlineDTONew);
                createOutlineNewList = createOutlineNewList.stream().map(c -> {
                    if (CollectionUtils.isEmpty(c.getCreateOutlineList())) {
                        c.setCreateOutlineList(null);
                    }
                    return c;
                }).collect(Collectors.toList());
                PromptWordsConfig ChatCreatePiecewisePromptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CHAT_CREATE_PIECEWISE_NEW, title, userSemantic, JsonUtils.toJSONString(createOutlineNewList));
                this.buildChatCreateBuffer(ChatCreatePiecewisePromptWordsConfig, contentBuffer, contentBufferAll, businessNo);
            }
        }

        String taskFeedback = "已为您完成" + title + "的文档撰写!";
        ChatCompletionChunkVO chatCompletionChunkVO3 = new ChatCompletionChunkVO();
        chatCompletionChunkVO3.setBusinessNo(businessNo2);
        chatCompletionChunkVO3.setNewContent(taskFeedback);
        chatCompletionChunkVO3.setChatRole(ChatRoleEnum.TASK_FEEDBACK);
        contentBuffer.add(chatCompletionChunkVO3);

        ChatHistoryDetailDTO chatHistoryDetailDTO = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO.setChatRole(ChatRoleEnum.DOCUMENT);
        chatHistoryDetailDTO.setChatContent(contentBufferAll.toString());
        chatHistoryDetailDTO.setBusinessNo(businessNo);
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO);
        ChatHistoryDetailDTO chatHistoryDetailDTO2 = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO2.setChatRole(ChatRoleEnum.TASK_FEEDBACK);
        chatHistoryDetailDTO2.setChatContent(taskFeedback);
        chatHistoryDetailDTO2.setBusinessNo(businessNo2);
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO2);

        log.info("聊天完成接口/非流式，文档大纲撰写，userSemantic：{}，contentLLM（正文）：{}", userSemantic, contentBufferAll);
    }

    private void buildChatCreateBuffer(PromptWordsConfig promptWordsConfig, Queue<Object> contentBuffer, StringBuffer contentBufferAll, String businessNo) {
        try {
            Response response = this.chatCompletionsStream(promptWordsConfig);
            BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().asInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isEmpty(line)) {
                    continue;
                } else if (line.contains("data: [DONE]")) {
                    log.info("Received event，任务结束：{}", line);
                    ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
                    chatCompletionChunkVO.setNewContent("\n");
                    chatCompletionChunkVO.setBusinessNo(businessNo);
                    chatCompletionChunkVO.setChatRole(ChatRoleEnum.DOCUMENT);
                    contentBuffer.add(chatCompletionChunkVO);
                    contentBufferAll.append("\n");
                    break;
                } else if (line.startsWith("data: ")) {
                    line = line.substring(6);
                }
                ChatCompletionChunkDTO chatCompletionChunkDTO = JsonUtils.parseObject(line, ChatCompletionChunkDTO.class);
                if (Objects.isNull(chatCompletionChunkDTO) || CollectionUtils.isEmpty(chatCompletionChunkDTO.getChoices())) {
                    continue;
                }
                for (ChatCompletionChunkDTO.Choice choice : chatCompletionChunkDTO.getChoices()) {
                    if (Objects.isNull(choice) || Objects.isNull(choice.getDelta()) || Objects.isNull(choice.getDelta().getContent())) {
                        continue;
                    }
                    String content = choice.getDelta().getContent();
                    if (StringUtils.isBlank(content)) {
                        continue;
                    }
                    ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
                    chatCompletionChunkVO.setChatRole(ChatRoleEnum.DOCUMENT);
                    chatCompletionChunkVO.setNewContent(content);
                    chatCompletionChunkVO.setBusinessNo(businessNo);
                    contentBuffer.add(chatCompletionChunkVO);
                    contentBufferAll.append(content);
                }
            }
        } catch (IOException e) {
            log.error("聊天完成接口/流式，请求异常，promptWordsConfig：{}，异常原因：{}", JsonUtils.toJSONString(promptWordsConfig), e);
        }
    }

    private void buildChatCreatePiecewiseBuffer(PromptWordsConfig promptWordsConfig, Queue<Object> contentBuffer, StringBuffer contentBufferAll) {
        try {
            Response response = this.chatCompletionsStream(promptWordsConfig);
            BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().asInputStream()));
            String line;
            StringBuffer processingBuffer = new StringBuffer(); // 这个缓冲区将保存所有累积的内容，用于纠错和发送

            while ((line = reader.readLine()) != null) {
                if (line.contains("data: [DONE]")) {
                    log.info("Received event，任务结束：{}", line);
                    // 处理 processingBuffer 中剩余的所有内容
                    if (processingBuffer.length() > 0) {
//                        String finalContentToProcess = processingBuffer.toString().replace("---", "");
                        String correctedFinalContent = correctMarkdownHeadings(processingBuffer.toString());

                        ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
                        chatCompletionChunkVO.setNewContent(correctedFinalContent + "\n");
                        chatCompletionChunkVO.setChatRole(ChatRoleEnum.ASSISTANT);
                        contentBuffer.add(chatCompletionChunkVO);
                        contentBufferAll.append(correctedFinalContent).append("\n");
                    } else {
                        // 如果没有内容但收到 [DONE]，仍然根据原始逻辑添加换行符到 contentBufferAll
                        contentBufferAll.append("\n");
                    }
                    break;
                } else if (line.startsWith("data: ")) {
                    line = line.substring(6);
                }
                ChatCompletionChunkDTO chatCompletionChunkDTO = JsonUtils.parseObject(line, ChatCompletionChunkDTO.class);
                if (Objects.isNull(chatCompletionChunkDTO) || CollectionUtils.isEmpty(chatCompletionChunkDTO.getChoices())) {
                    continue;
                }
                for (ChatCompletionChunkDTO.Choice choice : chatCompletionChunkDTO.getChoices()) {
                    if (Objects.isNull(choice) || Objects.isNull(choice.getDelta()) || Objects.isNull(choice.getDelta().getContent())) {
                        continue;
                    }
                    String contentDelta = choice.getDelta().getContent();
                    processingBuffer.append(contentDelta); // 将新的增量内容追加到处理缓冲区

                    // 每次接收到新内容时，对整个 processingBuffer 进行纠正，以捕获跨片段的模式
                    String correctedBufferContent = correctMarkdownHeadings(processingBuffer.toString());
                    // 将纠正后的内容更新回 processingBuffer，以便后续的提取和纠正操作都在正确的数据上进行
                    processingBuffer.setLength(0);
                    processingBuffer.append(correctedBufferContent);

                    // 从 processingBuffer 中提取并发送大约 80 个字符的块
                    while (processingBuffer.length() >= 80) {
                        String chunkToSend = processingBuffer.substring(0, 80);
                        ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
                        chatCompletionChunkVO.setNewContent(chunkToSend); // 仍然应用旧的替换逻辑
                        chatCompletionChunkVO.setChatRole(ChatRoleEnum.ASSISTANT);
                        contentBuffer.add(chatCompletionChunkVO);
                        contentBufferAll.append(chunkToSend); // 同时添加到完整内容的缓冲区
                        processingBuffer.delete(0, 80); // 从处理缓冲区中删除已发送的部分
                    }
                }
            }
        } catch (IOException e) {
            log.error("聊天完成接口/流式，请求异常，promptWordsConfig：{}，异常原因：{}", JsonUtils.toJSONString(promptWordsConfig), e);
        }
    }

    private void buildContentChatBufferDoran(PromptWordsConfig promptWordsConfig, AtomicBoolean done, StringBuffer contentBufferAll, StringBuffer contentBufferAll2) {
        new Thread(() -> {
            try {
                Response response = this.chatCompletionsStream(promptWordsConfig);
                BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().asInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    if (StringUtils.isEmpty(line)) {
                        continue;
                    } else if (line.contains("data: [DONE]")) {
                        log.info("Received event，任务结束：{}", line);
                        done.set(Boolean.TRUE);
                        break;
                    } else if (line.startsWith("data: ")) {
                        line = line.substring(6);
                    }
                    ChatCompletionChunkDTO chatCompletionChunkDTO = JsonUtils.parseObject(line, ChatCompletionChunkDTO.class);
                    if (Objects.isNull(chatCompletionChunkDTO) || CollectionUtils.isEmpty(chatCompletionChunkDTO.getChoices())) {
                        continue;
                    }
                    for (ChatCompletionChunkDTO.Choice choice : chatCompletionChunkDTO.getChoices()) {
                        if (Objects.isNull(choice) || Objects.isNull(choice.getDelta()) || Objects.isNull(choice.getDelta().getContent())) {
                            continue;
                        }
                        String content = choice.getDelta().getContent();
                        contentBufferAll.append(content);
                        contentBufferAll2.append(content);
//                        log.info("聊天完成接口/流式，line：{}", choice.getDelta().getContent());
                    }
                }
            } catch (IOException e) {
                log.error("聊天完成接口/流式，请求异常，promptWordsConfig：{}，异常原因：{}", JsonUtils.toJSONString(promptWordsConfig), e);
            }
        }).start();
    }

    private String getUserSemantic(List<DoronChatMessageDTO> messages) {
        if (messages.size() <= 1) {
            DoronChatMessageDTO doronChatMessageDTO = messages.get(0);
            List<DoronChatMessageDetailDTO> dtoMessageDetails = doronChatMessageDTO.getMessageDetailList();
            for (DoronChatMessageDetailDTO dtoMessageDetail : dtoMessageDetails) {
                if (dtoMessageDetail.getChatRole() == ChatRoleEnum.TEXT) {
                    return dtoMessageDetail.getChatContent();
                }
            }
        }
        DoronChatMessageDTO doronChatMessageDTO = messages.get(messages.size() - 1);
        if (ChatRoleEnum.ASSISTANT == doronChatMessageDTO.getChatRole() || ChatRoleEnum.TASK_FEEDBACK == doronChatMessageDTO.getChatRole()) {
            messages.remove(doronChatMessageDTO);
        }
        //根据用户chat上下文分析用户语义
        StringBuffer messagesStr = new StringBuffer();
        for (DoronChatMessageDTO message : messages) {
            ChatRoleEnum chatRole = message.getChatRole();
            int level = message.getLevel();
            for (DoronChatMessageDetailDTO messageDetail : message.getMessageDetailList()) {
                if (ChatRoleEnum.USER == chatRole && messageDetail.getChatRole() == ChatRoleEnum.TEXT) {
                    messagesStr.append(ChatRoleEnum.USER.getType()).append("(level ").append(level).append("): ").append(messageDetail.getChatContent()).append("\n");
                } else if (ChatRoleEnum.ASSISTANT == chatRole && (messageDetail.getChatRole() == ChatRoleEnum.ASSISTANT || messageDetail.getChatRole() == ChatRoleEnum.TASK_FEEDBACK)) {
                    messagesStr.append(ChatRoleEnum.ASSISTANT.getType()).append("(level ").append(level).append("): ").append(messageDetail.getChatContent()).append("\n");
                }
            }
        }
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CHAT_USER_SEMANTIC_EXTRACTION, messagesStr.toString());
        ChatCompletionResponseDTO chatCompletionResponseDTO = chatCompletions(promptWordsConfig);
        if (Objects.isNull(chatCompletionResponseDTO) || CollectionUtils.isEmpty(chatCompletionResponseDTO.getChoices()) ||
                Objects.isNull(chatCompletionResponseDTO.getChoices().get(0).getMessage()) ||
                StringUtils.isEmpty(chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent())) {
            return JsonUtils.toJSONString(messages);
        }
        return chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
    }

    private void doronChatCheck(DoronChatDTO doronChatDTO) {
        if (StringUtils.isEmpty(doronChatDTO.getSessionId())) {
            throw new BussinessBizException("会话ID为空！");
        }
        List<DoronChatMessageDTO> doronChatMessageDTOList = doronChatDTO.getMessages();
        if (CollectionUtils.isEmpty(doronChatMessageDTOList)) {
            throw new BussinessBizException("用户问题为空！");
        }
        doronChatMessageDTOList = doronChatMessageDTOList.stream().filter(d -> ChatRoleEnum.USER == d.getChatRole() || ChatRoleEnum.ASSISTANT == d.getChatRole()).collect(Collectors.toList());
        int size = doronChatMessageDTOList.size();
        if (size > 40) {
            doronChatMessageDTOList = doronChatMessageDTOList.subList(size - 15, size);
        }
        if (CollectionUtils.isEmpty(doronChatMessageDTOList)) {
            throw new BussinessBizException("请输入你想问的问题！");
        }
        for (int i = 0; i < doronChatMessageDTOList.size(); i++) {
            DoronChatMessageDTO doronChatMessageDTO = doronChatMessageDTOList.get(i);
            doronChatMessageDTO.setLevel(i + 1);
        }
        DoronChatMessageDTO doronChatMessageDTO = doronChatMessageDTOList.get(doronChatMessageDTOList.size() - 1);
        if (ChatRoleEnum.ASSISTANT == doronChatMessageDTO.getChatRole()) {
            doronChatMessageDTOList.remove(doronChatMessageDTO);
        }
        doronChatDTO.setMessages(doronChatMessageDTOList);
    }

    private void callTools(String sessionId, Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue, List<DoronChatMessageDetailDTO> doronChatMessageDetailDTOList, List<ToolDtlDTO> toolDtlDTOList, AtomicBoolean done, Boolean stream, String messagesStr) {
        log.info("开始构建模板库缓存数据，开始执行，done：{}", done.get());
        new Thread(() -> {
            long startTime = System.currentTimeMillis();
            long timeout = 1000000;
            while (true) {
                if (System.currentTimeMillis() - startTime > timeout) {
                    log.warn("开始构建模板库缓存数据，执行超时，done：{}", done.get());
                    break;
                }

                log.info("开始构建模板库缓存数据，执行中，done：{}，toolDtlDTOList：{}", done.get(), JsonUtils.toJSONString(toolDtlDTOList));
                this.autioFill(contentBuffer, chatHistoryDetailDTOQueue, toolDtlDTOList);
                //call模版tools
//                Boolean useTemplate = templateLibraryService.buildTemplateLibraryBuffer(contentBuffer, chatHistoryDetailDTOQueue, doronChatMessageDetailDTOList, toolDtlDTO, messagesStr);
                //call重写改写tools（原文内容抽取+改写）
//                editDocumentService.buildDoccontentRewriteBuffer(sessionId, contentBuffer, chatHistoryDetailDTOQueue, toolDtlDTO);
                //call自由写作tools
                this.buildFreelanceWritingBuffer(contentBuffer, chatHistoryDetailDTOQueue, toolDtlDTOList, doronChatMessageDetailDTOList);
                //call原文提取tools
                //editDocumentService.buildDoccontentExtractionBuffer(sessionId, contentBuffer, chatHistoryDetailDTOQueue, contentBufferAll);
                // 关键词替换工具
//                editDocumentService.callTextReplacement(sessionId, contentBuffer, chatHistoryDetailDTOQueue, toolDtlDTO);
                done.set(Boolean.TRUE);
                log.info("开始构建模板库缓存数据，执行完成，done：{}", done.get());
                return;
            }
        }).start();
    }

    private void autioFill(Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue, List<ToolDtlDTO> toolDtlDTOList) {
        if (CollectionUtils.isEmpty(toolDtlDTOList) || toolDtlDTOList.stream().anyMatch(t -> t.getToolLabels() == ToolLabelsEnum.FREELANCE_WRITING)) {
            return;
        }
        String businessNo2 = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
        ChatCompletionChunkVO chatCompletionChunkVO2 = new ChatCompletionChunkVO();
        chatCompletionChunkVO2.setNewContent(toolDtlDTOList);
        chatCompletionChunkVO2.setChatRole(ChatRoleEnum.AUTOFILL);
        chatCompletionChunkVO2.setBusinessNo(businessNo2);
        contentBuffer.add(chatCompletionChunkVO2);
        ChatHistoryDetailDTO chatHistoryDetailDTO2 = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO2.setChatRole(ChatRoleEnum.AUTOFILL);
        chatHistoryDetailDTO2.setChatContent(JsonUtils.toJSONString(toolDtlDTOList));
        chatHistoryDetailDTO2.setBusinessNo(businessNo2);
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO2);
    }

    private void buildFreelanceWritingBuffer(Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue, List<ToolDtlDTO> toolDtlDTOList, List<DoronChatMessageDetailDTO> doronChatMessageDetailDTOList) {
        if (CollectionUtils.isEmpty(toolDtlDTOList) || toolDtlDTOList.stream().noneMatch(t -> t.getToolLabels() == ToolLabelsEnum.FREELANCE_WRITING)) {
            return;
        }

        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CHAT_USER_SEMANTIC_EXTRACTION);
        PromptWordsConfigDTO promptWordsConfigDTO = new PromptWordsConfigDTO();
        BeanUtils.copyProperties(promptWordsConfig, promptWordsConfigDTO);
        promptWordsConfigDTO.setDoronChatMessageDetailDTOList(doronChatMessageDetailDTOList);
        ChatCompletionResponseDTO chatCompletionResponseDTO = chatCompletions(promptWordsConfigDTO);
        if (Objects.isNull(chatCompletionResponseDTO) || CollectionUtils.isEmpty(chatCompletionResponseDTO.getChoices()) ||
                Objects.isNull(chatCompletionResponseDTO.getChoices().get(0).getMessage()) ||
                StringUtils.isEmpty(chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent())) {
            return;
        }
        String topic = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
        log.info("自由撰写工具topic:{}", topic);

        ChatCompletionChunkVO chatCompletionChunkVO3 = new ChatCompletionChunkVO();
        String businessNo3 = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
        CreateOutlineVO createOutlineVO = editDocumentService.listCreateOutlineVO(topic);
        chatCompletionChunkVO3.setNewContent(createOutlineVO);
        chatCompletionChunkVO3.setChatRole(ChatRoleEnum.OUTLINE);
        chatCompletionChunkVO3.setBusinessNo(businessNo3);
        contentBuffer.add(chatCompletionChunkVO3);
        ChatHistoryDetailDTO chatHistoryDetailDTO3 = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO3.setChatRole(ChatRoleEnum.OUTLINE);
        chatHistoryDetailDTO3.setChatContent(JsonUtils.toJSONString(createOutlineVO));
        chatHistoryDetailDTO3.setBusinessNo(businessNo3);
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO3);
        //根据大纲写作
//        this.buildChatCreateBuffer(createOutlineVO, contentBuffer, chatHistoryDetailDTOQueue, topic);
    }


    private void buildContentBuffer(Queue<Object> contentBuffer, StringBuffer contentBufferAll, AtomicBoolean
            plan1, AtomicBoolean plan2) {
        new Thread(() -> {
            StringBuffer str = new StringBuffer(ToolLabelsEnum.TEMPLATE_LIBRARY.getType());
            StringBuffer str2 = new StringBuffer();
            int i = 0;
            while (true) {
                if (plan1.get() && StringUtils.isEmpty(contentBufferAll.toString()) && i < str.length()) {
                    ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
                    chatCompletionChunkVO.setNewContent(str2.toString());
                    chatCompletionChunkVO.setChatRole(ChatRoleEnum.ASSISTANT);
                    contentBuffer.add(chatCompletionChunkVO);
                    plan2.set(Boolean.TRUE);
                    break;
                }
                if (StringUtils.isEmpty(contentBufferAll.toString())) {
                    continue;
                }
                char getCharAt = contentBufferAll.charAt(0);
                char getCharAt2 = str.charAt(i);
                if (getCharAt == getCharAt2) {
                    i += 1;
                    str2.append(getCharAt);
                    contentBufferAll.deleteCharAt(0);
                    if (i == str.length()) {
                        plan2.set(Boolean.TRUE);
                        break;
                    }
                    continue;
                } else if (i > 0) {
                    ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
                    chatCompletionChunkVO.setNewContent(str2.toString());
                    chatCompletionChunkVO.setChatRole(ChatRoleEnum.ASSISTANT);
                    contentBuffer.add(chatCompletionChunkVO);
                    i -= 1;
                    str2.setLength(0);
                }

                ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
                chatCompletionChunkVO.setNewContent(String.valueOf(getCharAt));
                chatCompletionChunkVO.setChatRole(ChatRoleEnum.ASSISTANT);
                contentBuffer.add(chatCompletionChunkVO);
                contentBufferAll.deleteCharAt(0);
            }
        }).start();
    }

    private void chatCreateCheck(DocumentWritingDTO documentWritingDTO) {
        List<DoronChatMessageDTO> doronChatMessageDTOList = documentWritingDTO.getMessages();
        if (CollectionUtils.isEmpty(doronChatMessageDTOList)) {
            throw new BussinessBizException("用户问题为空！");
        }
        doronChatMessageDTOList = doronChatMessageDTOList.stream().filter(d -> ChatRoleEnum.USER == d.getChatRole() || ChatRoleEnum.ASSISTANT == d.getChatRole()).collect(Collectors.toList());
        int size = doronChatMessageDTOList.size();
        if (size > 15) {
            doronChatMessageDTOList = doronChatMessageDTOList.subList(size - 15, size);
        }
        if (CollectionUtils.isEmpty(doronChatMessageDTOList)) {
            throw new BussinessBizException("请输入你想问的问题！");
        }
        for (int i = 0; i < doronChatMessageDTOList.size(); i++) {
            DoronChatMessageDTO doronChatMessageDTO = doronChatMessageDTOList.get(i);
            doronChatMessageDTO.setLevel(i + 1);
        }
        documentWritingDTO.setMessages(doronChatMessageDTOList);
    }

    private void buildContentBuffer(PromptWordsConfig promptWordsConfig, Queue<Object> contentBuffer, AtomicBoolean done) {
        new Thread(() -> {
            try {
                Response response = this.chatCompletionsStream(promptWordsConfig);
                BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().asInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    if (org.apache.commons.lang3.StringUtils.isEmpty(line)) {
                        continue;
                    } else if (line.contains("data: [DONE]")) {
                        log.info("Received event，任务结束：{}", line);
                        done.set(Boolean.TRUE);
                        break;
                    } else if (line.startsWith("data: ")) {
                        line = line.substring(6);
                    }
                    ChatCompletionChunkDTO chatCompletionChunkDTO = JsonUtils.parseObject(line, ChatCompletionChunkDTO.class);
                    if (Objects.isNull(chatCompletionChunkDTO) || CollectionUtils.isEmpty(chatCompletionChunkDTO.getChoices())) {
                        continue;
                    }
                    for (ChatCompletionChunkDTO.Choice choice : chatCompletionChunkDTO.getChoices()) {
                        if (Objects.isNull(choice) || Objects.isNull(choice.getDelta().getContent())) {
                            continue;
                        }
                        ChatCompletionChunkVO provisionSearchStreamVO = new ChatCompletionChunkVO();
                        provisionSearchStreamVO.setNewContent(choice.getDelta().getContent());
                        provisionSearchStreamVO.setChatRole(ChatRoleEnum.ASSISTANT);
                        contentBuffer.add(provisionSearchStreamVO);
//                        contentBuffer.add(choice.getDelta().getContent());
                        log.info("聊天完成接口/流式，line：{}", choice.getDelta().getContent());
                    }
                }
            } catch (IOException e) {
                log.error("聊天完成接口/流式，请求异常，promptWordsConfig：{}，异常原因：{}", JsonUtils.toJSONString(promptWordsConfig), e);
            }
        }).start();
    }

    private Flux<String> buildFlux(Queue<Object> contentBuffer, AtomicBoolean done) {
        return Flux.<String>generate(sink -> {
                    Object obj = contentBuffer.poll();
                    if (obj != null) {
                        sink.next(JsonUtils.toJSONString(obj));
                    } else if (done.get()) {
                        sink.next("DONE");
                        sink.complete();
                    } else {
                        sink.next(""); // 必须调用sink方法，否则会抛异常
                    }
                })
                .delayElements(Duration.ofMillis(3))
                .onBackpressureBuffer(2000)
                .take(Duration.ofMinutes(8))
                .filter(str -> !str.isEmpty());
    }

    private void addChatHistoryDetailDTOQueue(DoronChatDTO doronChatDTO, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue) {
        ChatHistoryDetailDTO chatHistoryDetailDTO = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO.setChatRole(ChatRoleEnum.USER);
        List<DoronChatMessageDetailDTO> messageDetails = doronChatDTO.getMessages().get(doronChatDTO.getMessages().size() - 1).getMessageDetailList();
        List<ChatHistoryDetailExcerpt> chatHistoryDetailExcerptList = Lists.newArrayList();
        for (DoronChatMessageDetailDTO doronChatMessageDetailDTO : messageDetails) {
            if (ChatRoleEnum.TEXT == doronChatMessageDetailDTO.getChatRole()) {
                chatHistoryDetailDTO.setChatContent(doronChatMessageDetailDTO.getChatContent());
                continue;
            }
            ChatHistoryDetailExcerpt chatHistoryDetailExcerpt = new ChatHistoryDetailExcerpt();
            BeanUtils.copyProperties(doronChatMessageDetailDTO, chatHistoryDetailExcerpt);
            chatHistoryDetailExcerpt.setMsgType(doronChatMessageDetailDTO.getChatRole());
            chatHistoryDetailExcerptList.add(chatHistoryDetailExcerpt);
        }
        chatHistoryDetailDTO.setChatHistoryDetailExcerptList(chatHistoryDetailExcerptList);
        chatHistoryDetailDTO.setBusinessNo(joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX));
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO);
    }

    /**
     * 纠正Markdown标题和类似标题的格式，例如"###1.1"纠正为"### 1.1"，"***1.1.1"纠正为"*** 1.1.1"。
     *
     * @param text 待纠正的文本
     * @return 纠正后的文本
     */
    private String correctMarkdownHeadings(String text) {
        // 匹配 # 后紧跟数字（例如 ###1.1、##2.1.3）
        // (\#+) 匹配一个或多个 # 符号并捕获为第一组
        // (\d+(\.\d+)*) 匹配数字及小数点数字组合（例如 1.1、2.1.3）并捕获为第二组
        Pattern hashHeadingPattern = Pattern.compile("(#+)(\\d+(\\.\\d+)*)");
        Matcher hashHeadingMatcher = hashHeadingPattern.matcher(text);
        StringBuffer correctedTextBuffer = new StringBuffer();
        while (hashHeadingMatcher.find()) {
            hashHeadingMatcher.appendReplacement(correctedTextBuffer, hashHeadingMatcher.group(1) + " " + hashHeadingMatcher.group(2));
        }
        hashHeadingMatcher.appendTail(correctedTextBuffer);
        String result = correctedTextBuffer.toString();

        // 匹配 *** 后紧跟数字（例如 ***1.1.1）。假设这是用户想要纠正的类似标题的格式问题。
        // (\*\*\*) 匹配 *** 并捕获为第一组（需要转义星号）
        // (\d+(\.\d+)*) 匹配数字及小数点数字组合（例如 1.1.1）并捕获为第二组
        Pattern tripleAsteriskHeadingPattern = Pattern.compile("(\\*\\*\\*)(\\d+(\\.\\d+)*)");
        Matcher tripleAsteriskHeadingMatcher = tripleAsteriskHeadingPattern.matcher(result);
        correctedTextBuffer = new StringBuffer(); // 重置 StringBuffer 用于第二轮替换
        while (tripleAsteriskHeadingMatcher.find()) {
            tripleAsteriskHeadingMatcher.appendReplacement(correctedTextBuffer, tripleAsteriskHeadingMatcher.group(1) + " " + tripleAsteriskHeadingMatcher.group(2));
        }
        tripleAsteriskHeadingMatcher.appendTail(correctedTextBuffer);
        result = correctedTextBuffer.toString();

        // 匹配 ** 后紧跟数字，且数字后没有立即闭合的 **。将其纠正为 ** 数字 **
        // (\*\*) 匹配 ** 并捕获为第一组
        // (\d+(\.\d+)*) 匹配数字及小数点数字组合并捕获为第二组
        // (?!\\*\\*) 负向先行断言，确保不匹配已经有闭合 ** 的情况
        Pattern doubleAsteriskBoldPattern = Pattern.compile("(\\*\\*)(\\d+(\\.\\d+)*)(?!\\*\\*)");
        Matcher doubleAsteriskBoldMatcher = doubleAsteriskBoldPattern.matcher(result);
        correctedTextBuffer = new StringBuffer(); // 重置 StringBuffer 用于第三轮替换
        while (doubleAsteriskBoldMatcher.find()) {
            doubleAsteriskBoldMatcher.appendReplacement(correctedTextBuffer, doubleAsteriskBoldMatcher.group(1) + " " + doubleAsteriskBoldMatcher.group(2) + doubleAsteriskBoldMatcher.group(1));
        }
        doubleAsteriskBoldMatcher.appendTail(correctedTextBuffer);
        result = correctedTextBuffer.toString();

        return result;
    }
}

