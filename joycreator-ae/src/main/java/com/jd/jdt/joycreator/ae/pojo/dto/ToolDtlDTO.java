package com.jd.jdt.joycreator.ae.pojo.dto;

import com.jd.jdt.joycreator.ae.enums.ToolLabelsEnum;
import lombok.Data;

import java.util.Map;

/**
 * <p>
 * ToolDtlDTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Data
public class ToolDtlDTO {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 工具类型
     */
    private ToolLabelsEnum toolLabels;

    /**
     * 工具参数
     */
    private Map<String, Object> toolParams;

    /**
     * 是否最后一个
     */
    private boolean last;

}
