package com.jd.jdt.joycreator.ae.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.jdt.app4s.component.archetype.common.holder.LcdpLoginContext;
import com.jd.jdt.app4s.component.archetype.common.holder.LcdpUserInfo;
import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jdt.joycreator.ae.dao.plus.IChatHistoryDetailMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IChatHistoryDetailTemplateMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IChatHistoryMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IIpmsUserRoleMapper;
import com.jd.jdt.joycreator.ae.entity.ChatHistory;
import com.jd.jdt.joycreator.ae.entity.ChatHistoryDetail;
import com.jd.jdt.joycreator.ae.entity.IpmsUserRole;
import com.jd.jdt.joycreator.ae.pojo.dto.CollectionDTO;
import com.jd.jdt.joycreator.ae.enums.JoyCreateBusinessTypeEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.HrResultDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.HrUserBasicDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.BaseQueryVO;
import com.jd.jdt.joycreator.ae.pojo.vo.ChatHistoryDetailTemplateVO;
import com.jd.jdt.joycreator.ae.pojo.vo.ChatHistoryListVO;
import com.jd.jdt.joycreator.ae.pojo.vo.JdrUserVO;
import com.jd.jdt.joycreator.ae.rpc.jsf.HrUserServiceHelp;
import com.jd.jdt.joycreator.ae.service.ChatHistoryService;
import com.jd.jdt.joycreator.ae.utils.JoyCreatorUtil;
import com.jd.jsf.gd.util.StringUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 聊天记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Log4j2
@Service
public class ChatHistoryServiceImpl implements ChatHistoryService {

    @Autowired
    private IChatHistoryMapper iChatHistoryMapper;
    @Autowired
    private IChatHistoryDetailMapper iChatHistoryDetailMapper;
    @Autowired
    private IChatHistoryDetailTemplateMapper iChatHistoryDetailTemplateMapper;
    @Autowired
    private JoyCreatorUtil joyCreatorUtil;
    @Autowired
    private IIpmsUserRoleMapper iIpmsUserRoleMapper;
    @Autowired
    private HrUserServiceHelp hrUserServiceHelp;

    @Override
    public String create() {
        LcdpUserInfo user = LcdpLoginContext.getUser();
        String userName = user.getUserName();
        log.info("创建聊天记录sessionId，userName:{}", userName);
        String sessionId = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LS);
        iChatHistoryMapper.create(sessionId);
        log.info("创建聊天记录sessionI：{}，userName:{}", sessionId, userName);
        return sessionId;
    }

    @Override
    public List<ChatHistory> list() {
        LcdpUserInfo user = LcdpLoginContext.getUser();
        String userName = user.getUserName();
        List<ChatHistory> chatHistoryList = iChatHistoryMapper.listChatHistoryDetailByErp(userName);
        return chatHistoryList;
    }

    @Override
    public Page<ChatHistoryListVO> searchChatHistoryListVO(BaseQueryVO<String> baseQueryVO) {
        LcdpUserInfo user = LcdpLoginContext.getUser();
        String userName = user.getUserName();
        Page page = baseQueryVO.getPage();
        QueryWrapper queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("yn", 1);
        if (!this.isAdmin(userName)) {
            queryWrapper.eq("erp", userName);
        }
        String param = baseQueryVO.getParam();
        if (StringUtils.isNotBlank(param)) {
            queryWrapper.like("name", param);
        }
        queryWrapper.orderByDesc("id");
        Page<ChatHistoryListVO> pageResult = iChatHistoryMapper.searchChatHistoryListVO(page, queryWrapper);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return pageResult;
        }

        pageResult.getRecords().forEach(i -> {
            HrResultDTO<HrUserBasicDTO> hrResultDTO = hrUserServiceHelp.queryUserByErp(i.getErp());
            if (Objects.nonNull(hrResultDTO) && hrResultDTO.getIsSuccess()) {
                JdrUserVO jdrUserVO = new JdrUserVO();
                HrUserBasicDTO responsebody = hrResultDTO.getResponsebody();
                jdrUserVO.setUserName(responsebody.getUserName());
                jdrUserVO.setRealName(responsebody.getRealName());
                jdrUserVO.setHeadImage(responsebody.getHeadImg());
                jdrUserVO.setPositionName(responsebody.getPositionName());
                jdrUserVO.setFullDetpName(responsebody.getOrganizationFullName());
                i.setJdrUserVO(jdrUserVO);
            }
            List<ChatHistoryDetailTemplateVO> chatHistoryDetailTemplateVOList = iChatHistoryDetailTemplateMapper.listChatHistoryDetailTemplateByChatHistoryId(i.getId());
            i.setChatHistoryDetailTemplateList(chatHistoryDetailTemplateVOList);
            ChatHistoryDetail chatHistoryDetail = iChatHistoryDetailMapper.chatHistoryDetailTemplateByLimitOne(i.getId());
            if (Objects.isNull(chatHistoryDetail)) {
                i.setName("新的会话");
                return;
            }
            i.setName(chatHistoryDetail.getChatContent());
        });
        return pageResult;
    }

    @Override
    public Boolean updateCollection(CollectionDTO collectionDTO) {
        return iChatHistoryMapper.updateCollection(collectionDTO);
    }

    @Override
    public Boolean del(String sessionId) {
        ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(sessionId);
        if (Objects.isNull(chatHistory)) {
            throw new BussinessBizException("聊天记录不存在");
        }

        iChatHistoryDetailTemplateMapper.delByChatHistoryId(chatHistory.getId());
        iChatHistoryDetailMapper.delByChatHistoryId(chatHistory.getId());
        iChatHistoryMapper.removeById(chatHistory.getId());
        return Boolean.TRUE;
    }

    private boolean isAdmin(String userName) {
        List<IpmsUserRole> ipmsUserRoleList = iIpmsUserRoleMapper.getIpmsUserRoleByErp(userName);
        if (CollectionUtils.isEmpty(ipmsUserRoleList)) {
            return Boolean.FALSE;
        }
        return ipmsUserRoleList.stream().anyMatch(i -> "admin".equals(i.getRole()));
    }
}
