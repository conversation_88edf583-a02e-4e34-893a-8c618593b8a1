package com.jd.jdt.joycreator.ae.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.jdt.app4s.component.archetype.common.holder.LcdpLoginContext;
import com.jd.jdt.app4s.component.archetype.common.holder.LcdpUserInfo;
import com.jd.jdt.app4s.component.common.api.exception.BussinessBizException;
import com.jd.jdt.joybuilder.permission.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.dao.plus.IChatHistoryMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IEditDocumentMapper;
import com.jd.jdt.joycreator.ae.dao.plus.IFileMapper;
import com.jd.jdt.joycreator.ae.entity.*;
import com.jd.jdt.joycreator.ae.enums.*;
import com.jd.jdt.joycreator.ae.pojo.dto.*;
import com.jd.jdt.joycreator.ae.pojo.vo.*;
import com.jd.jdt.joycreator.ae.rpc.feign.OfficeConvertService;
import com.jd.jdt.joycreator.ae.service.EditDocumentService;
import com.jd.jdt.joycreator.ae.service.ChatStudioService;
import com.jd.jdt.joycreator.ae.service.FileService;
import com.jd.jdt.joycreator.ae.service.PromptWordsConfigService;
import com.jd.jdt.joycreator.ae.utils.*;
import com.jd.jsf.gd.util.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.aspose.pdf.internal.l52j.l4n.e;

/**
 * <p>
 * 编辑器文档数据 服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Log4j2
@Service
public class EditDocumentServiceImpl implements EditDocumentService {

    @Autowired
    private IEditDocumentMapper iEditDocumentMapper;
    @Autowired
    private IChatHistoryMapper iChatHistoryMapper;
    @Autowired
    @Lazy
    private ChatStudioService editService;
    @Autowired
    private MarkdownParsingUtils markdownParsingUtils;
    @Autowired
    private PromptWordsConfigService promptWordsConfigService;
    @Autowired
    private JoyCreatorUtil joyCreatorUtil;
    @Autowired
    private IFileMapper iFileMapper;
    @Autowired
    private FileService fileService;
    @Autowired
    private OfficeConvertService officeConvertService;


    @Override
    public Long save(EditDocumentDTO editDocumentDTO) {
        log.info("编辑器文档数据保存，editDocumentDTO：{}", JsonUtils.toJSONString(editDocumentDTO));
        iChatHistoryMapper.create(editDocumentDTO.getSessionId());
        ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(editDocumentDTO.getSessionId());
        EditDocument editDocument = iEditDocumentMapper.dtlByChatHistoryId(chatHistory.getId());
        editDocument.setName(editDocumentDTO.getName());
        editDocument.setDocumentContent(editDocumentDTO.getDocumentContent());
        editDocument.setTextContent(editDocumentDTO.getTextContent());
        boolean saveOrUpdate = iEditDocumentMapper.updateById(editDocument);
        log.info("编辑器文档数据保存，saveOrUpdate：{}，editDocument：{}", saveOrUpdate, JsonUtils.toJSONString(editDocument));
        if (!saveOrUpdate) {
            throw new BussinessBizException("保存失败");
        }
        return editDocument.getId();
    }

    @Override
    public EditDocumentVO dtl(String sessionId) {
        log.info("编辑器文档数据查询，sessionId：{}", sessionId);
        ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(sessionId);
        if (Objects.isNull(chatHistory)) {
            return null;
        }
        EditDocument editDocument = iEditDocumentMapper.dtlByChatHistoryId(chatHistory.getId());
        if (Objects.isNull(editDocument)) {
            return null;
        }
        EditDocumentVO editDocumentVO = new EditDocumentVO();
        BeanUtils.copyProperties(editDocument, editDocumentVO);
        editDocumentVO.setSessionId(sessionId);
        log.info("编辑器文档数据查询，sessionId：{}，editDocumentVO：{}", sessionId, JsonUtils.toJSONString(editDocumentVO));
        return editDocumentVO;
    }
/*
    @Override
    public void buildDoccontentExtractionBuffer(String sessionId, Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue, ToolDtlDTO toolDtlDTO) {
        if (Objects.isNull(toolDtlDTO) || toolDtlDTO.getToolLabels() != ToolLabelsEnum.DOCCONTENT_REWRITE) {
            return;
        }
        Object doccontentExtractionKeyword = toolDtlDTO.getToolParams().get("doccontent_extraction_keyword");
        if (Objects.isNull(doccontentExtractionKeyword)) {
            return;
        }
        String extractContentDoc = this.textExtraction(sessionId, doccontentExtractionKeyword.toString());
        ChatCompletionChunkVO chatCompletionChunkVO = new ChatCompletionChunkVO();
        chatCompletionChunkVO.setNewContent(extractContentDoc);
        chatCompletionChunkVO.setChatRole(ChatRoleEnum.DOCCONTENT_EXTRACTION);
        contentBuffer.add(chatCompletionChunkVO);

        ChatHistoryDetailDTO chatHistoryDetailDTO = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO.setChatRole(ChatRoleEnum.DOCCONTENT_EXTRACTION);
        chatHistoryDetailDTO.setChatContent(extractContentDoc);
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO);
    }

    @Override
    public void buildDoccontentRewriteBuffer(String sessionId, Queue<Object> contentBuffer, Queue<ChatHistoryDetailDTO> chatHistoryDetailDTOQueue, ToolDtlDTO toolDtlDTO) {
        //原文改写意图
        if (Objects.isNull(toolDtlDTO) || toolDtlDTO.getToolLabels() != ToolLabelsEnum.DOCCONTENT_REWRITE) {
            return;
        }
        Object doccontentRewriteKeyword = toolDtlDTO.getToolParams().get("doccontent_rewrite_keyword");
        Object doccontentRewriteStyle = toolDtlDTO.getToolParams().get("doccontent_rewrite_style");
        if (Objects.isNull(doccontentRewriteKeyword)) {
            return;
        }
        Map<String, String> doccontentRewriteMap = Maps.newHashMap();
        doccontentRewriteMap.put("doccontentRewriteKeyword", doccontentRewriteKeyword.toString());
        doccontentRewriteMap.put("doccontentRewriteStyle", doccontentRewriteStyle.toString());
        ChatCompletionChunkVO chatCompletionChunkVO1 = new ChatCompletionChunkVO();
        String businessNo1 = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
        chatCompletionChunkVO1.setNewContent(JsonUtils.toJSONString(doccontentRewriteMap));
        chatCompletionChunkVO1.setChatRole(ChatRoleEnum.DOCCONTENT_REWRITE);
        chatCompletionChunkVO1.setBusinessNo(businessNo1);
        contentBuffer.add(chatCompletionChunkVO1);
        ChatCompletionChunkVO chatCompletionChunkVO2 = new ChatCompletionChunkVO();
        String businessNo2 = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
        chatCompletionChunkVO2.setNewContent("开始提取原文......");
        chatCompletionChunkVO2.setChatRole(ChatRoleEnum.DOCCONTENT_EXTRACTION_QUERY);
        chatCompletionChunkVO2.setBusinessNo(businessNo2);
        contentBuffer.add(chatCompletionChunkVO2);
        String extractContentDoc = this.textExtraction(sessionId, doccontentRewriteKeyword.toString());

        ChatCompletionChunkVO chatCompletionChunkVO3 = null;
        ChatCompletionChunkVO chatCompletionChunkVO4 = null;
        if (StringUtils.isEmpty(extractContentDoc) || extractContentDoc.contains("提取内容超限")) {
            String businessNo4 = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
            chatCompletionChunkVO4 = new ChatCompletionChunkVO();
            if (StringUtils.isEmpty(extractContentDoc)) {
                chatCompletionChunkVO4.setNewContent("(◎_◎;) 暂未搜索到原文信息、建议再尝试下更精准的描述。");
            } else {
                chatCompletionChunkVO4.setNewContent("(◎_◎;) 搜索到内容过多，建议再尝试下更精准的描述。");
            }
            chatCompletionChunkVO4.setChatRole(ChatRoleEnum.DOCCONTENT_EXTRACTION_ERROR);
            chatCompletionChunkVO4.setBusinessNo(businessNo4);
            contentBuffer.add(chatCompletionChunkVO4);
        } else {
            chatCompletionChunkVO3 = new ChatCompletionChunkVO();
            String businessNo3 = joyCreatorUtil.getBusinessNo(JoyCreateBusinessTypeEnum.LSMX);
            chatCompletionChunkVO3.setNewContent(extractContentDoc);
            chatCompletionChunkVO3.setChatRole(ChatRoleEnum.DOCCONTENT_EXTRACTION);
            chatCompletionChunkVO3.setBusinessNo(businessNo3);
            contentBuffer.add(chatCompletionChunkVO3);
        }

        //chat记录留痕
        ChatHistoryDetailDTO chatHistoryDetailDTO = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO.setChatRole(ChatRoleEnum.DOCCONTENT_REWRITE);
        chatHistoryDetailDTO.setChatContent(JsonUtils.toJSONString(doccontentRewriteMap));
        chatHistoryDetailDTO.setBusinessNo(businessNo1);
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO);
        ChatHistoryDetailDTO chatHistoryDetailDTO2 = new ChatHistoryDetailDTO();
        chatHistoryDetailDTO2.setChatRole(ChatRoleEnum.DOCCONTENT_EXTRACTION_QUERY);
        chatHistoryDetailDTO2.setChatContent("开始提取原文......");
        chatHistoryDetailDTO2.setBusinessNo(businessNo2);
        chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO2);
        if (Objects.nonNull(chatCompletionChunkVO3)) {
            ChatHistoryDetailDTO chatHistoryDetailDTO3 = new ChatHistoryDetailDTO();
            chatHistoryDetailDTO3.setChatRole(ChatRoleEnum.DOCCONTENT_EXTRACTION);
            chatHistoryDetailDTO3.setChatContent(chatCompletionChunkVO3.getNewContent().toString());
            chatHistoryDetailDTO3.setBusinessNo(chatCompletionChunkVO3.getBusinessNo());
            chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO3);
        }
        if (Objects.nonNull(chatCompletionChunkVO4)) {
            ChatHistoryDetailDTO chatHistoryDetailDTO4 = new ChatHistoryDetailDTO();
            chatHistoryDetailDTO4.setChatRole(ChatRoleEnum.DOCCONTENT_EXTRACTION_ERROR);
            chatHistoryDetailDTO4.setChatContent(chatCompletionChunkVO4.getNewContent().toString());
            chatHistoryDetailDTO4.setBusinessNo(chatCompletionChunkVO4.getBusinessNo());
            chatHistoryDetailDTOQueue.add(chatHistoryDetailDTO4);
        }

    }*/

    @Override
    public CreateOutlineVO listCreateOutlineVO(String tuserSemantic) {
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CHAT_CREATE_OUTLINE, tuserSemantic);
        ChatCompletionResponseDTO chatCompletionResponseDTO = editService.chatCompletions(promptWordsConfig);
        if (Objects.isNull(chatCompletionResponseDTO) || CollectionUtils.isEmpty(chatCompletionResponseDTO.getChoices()) ||
                Objects.isNull(chatCompletionResponseDTO.getChoices().get(0).getMessage()) ||
                StringUtils.isEmpty(chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent())) {
            return null;
        }
        return markdownParsingUtils.parseOutline(chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent());
    }

    @Override
    public String convertHtml(Long fileId) throws IOException {
        if (Objects.isNull(fileId)) {
            throw new BussinessBizException("文件id为空!");
        }
        File file = iFileMapper.getById(fileId);
        if (Objects.isNull(file)) {
            throw new BussinessBizException("文件不存在!");
        }
        InputStream inputStream = fileService.downLoad(file.getName());
        OfficeConvertDTO officeConvertDTO = new OfficeConvertDTO();
        officeConvertDTO.setFileName(file.getName());
        officeConvertDTO.setOfficeConvert(OfficeConvertEnum.DOCX_TO_HTML);
        officeConvertDTO.setByteArray(IOUtils.toByteArray(inputStream));
        ResponseResult<OfficeConvertVO> responseResult = officeConvertService.officeConvert(officeConvertDTO);
        if (responseResult == null || !responseResult.isSuccess() || responseResult.getData() == null) {
            throw new BussinessBizException("文件转换失败");
        }
        OfficeConvertVO officeConvertVO = responseResult.getData();
        byte[] convertedBytes = officeConvertVO.getByteArray();
        return new String(convertedBytes, StandardCharsets.UTF_8);
    }

    @Override
    public AutofillExtractionVO autofillExtraction(ToolDtlDTO toolDtlDTO) {
        Map<String, Object> toolParams = toolDtlDTO.getToolParams();
        if (StringUtils.equals(ChatRoleEnum.TEXT_REPLACEMENT.getType(), toolDtlDTO.getToolLabels().name())) {
            String textReplacementCommand = toolParams.get("textReplacementCommand").toString();
            String replaceExtraction = this.replaceExtraction(toolDtlDTO.getSessionId(), textReplacementCommand);
            if (StringUtils.isEmpty(replaceExtraction)) {
                return null;
            }
            DocumentReplaceDTO documentReplaceDTO = joyCreatorUtil.parseDocumentReplaceJson(replaceExtraction);
            if (Objects.isNull(documentReplaceDTO) || StringUtils.isEmpty(documentReplaceDTO.getOldContent()) || StringUtils.isEmpty(documentReplaceDTO.getNewContent())) {
                return null;
            }
            AutofillExtractionVO autofillExtractionVO = new AutofillExtractionVO();
            autofillExtractionVO.setRewriteType(toolDtlDTO.getToolLabels());
            autofillExtractionVO.setOldContent(documentReplaceDTO.getOldContent());
            autofillExtractionVO.setNewContent(documentReplaceDTO.getNewContent());
            return autofillExtractionVO;
        } else if (StringUtils.equals(ChatRoleEnum.DOCCONTENT_REWRITE.getType(), toolDtlDTO.getToolLabels().name())) {
            String doccontentRewriteKeyword = toolParams.get("doccontentRewriteKeyword").toString();
            String textExtraction = this.textExtraction(toolDtlDTO.getSessionId(), doccontentRewriteKeyword);
            if (StringUtils.isEmpty(textExtraction)) {
                return null;
            }
            AutofillExtractionVO autofillExtractionVO = new AutofillExtractionVO();
            autofillExtractionVO.setRewriteType(toolDtlDTO.getToolLabels());
            autofillExtractionVO.setOriginalParagraph(textExtraction);
            return autofillExtractionVO;
        }
        return null;
    }

    @Override
    public List<EditDocumentVO> list() {
        LcdpUserInfo user = LcdpLoginContext.getUser();
        String userName = user.getUserName();
        LambdaQueryWrapper<EditDocument> documentLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        if (!joyCreatorUtil.isAdmin(userName)) {
//            documentLambdaQueryWrapper.eq(EditDocument::getCreatedUser, userName);
//        }
        documentLambdaQueryWrapper
                .eq(EditDocument::getCreatedUser, userName)
                .select(EditDocument::getId, EditDocument::getName, EditDocument::getChatHistoryId, EditDocument::getCreatedUser, EditDocument::getCreateTime)
                .orderByDesc(EditDocument::getUpdateTime);

        List<EditDocument> editDocumentList = iEditDocumentMapper.list(documentLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(editDocumentList)) {
            return Lists.newArrayList();
        }

        List<Long> chatHistoryIdList = editDocumentList.stream().map(EditDocument::getChatHistoryId).collect(Collectors.toList());
        List<ChatHistory> chatHistoryList = iChatHistoryMapper.chatHistoryByIds(chatHistoryIdList);
        Map<Long, String> finalSessionIdMap;
        if (CollectionUtils.isNotEmpty(chatHistoryIdList)) {
            finalSessionIdMap = chatHistoryList.stream().collect(Collectors.toMap(ChatHistory::getId, ChatHistory::getSessionId));
        } else {
            finalSessionIdMap = Maps.newHashMap();
        }
        return editDocumentList.stream().map(e -> {
            EditDocumentVO editDocumentVO = new EditDocumentVO();
            BeanUtils.copyProperties(e, editDocumentVO);
            String sessionId = finalSessionIdMap.get(e.getChatHistoryId());
            editDocumentVO.setSessionId(sessionId);
            return editDocumentVO;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean del(String sessionId) {
        if (StringUtils.isEmpty(sessionId)) {
            throw new BussinessBizException("请求必要参数sessionId不能为空");
        }
        ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(sessionId);
        if (Objects.isNull(chatHistory)) {
            throw new BussinessBizException("删除的文件不存在");
        }
        Boolean delledBySessionId = iChatHistoryMapper.delBySessionId(sessionId);
        if (!delledBySessionId) {
            throw new BussinessBizException("删除失败");
        }
        boolean remove = iEditDocumentMapper.remove(new LambdaQueryWrapper<EditDocument>().eq(EditDocument::getChatHistoryId, chatHistory.getId()));
        if (!remove) {
            throw new BussinessBizException("删除失败");
        }
        return Boolean.TRUE;
    }

    private String replaceExtraction(String sessionId, String extraction) {
        EditDocumentVO editDocumentVO = this.dtl(sessionId);
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.DOCUMENT_REPLACE, extraction, editDocumentVO.getTextContent());
        ChatCompletionResponseDTO chatCompletionResponseDTO = editService.chatCompletions(promptWordsConfig);
        return chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
    }


    private String textExtraction(String sessionId, String extraction) {
        //获取原文
        EditDocumentVO editDocumentVO = this.dtl(sessionId);
        if (Objects.isNull(editDocumentVO) || StringUtils.isEmpty(editDocumentVO.getTextContent())) {
            return null;
        }
//        String documentContent = markdownParsingUtils.convertHtmlToMarkdown(editDocumentVO.getDocumentContent());
        String textContent = editDocumentVO.getTextContent();
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CHAT_TEXT_EXTRACTION, extraction, textContent);
        ChatCompletionResponseDTO chatCompletionResponseDTO = editService.chatCompletions(promptWordsConfig);
        if (Objects.isNull(chatCompletionResponseDTO) || CollectionUtils.isEmpty(chatCompletionResponseDTO.getChoices()) || Objects.isNull(chatCompletionResponseDTO.getChoices().get(0).getMessage())) {
            return null;
        }
        String content = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
        log.info("模型提取到的原文内容：{}", content);
        if (StringUtils.isEmpty(content) || content.contains("提取内容超限")) {
            return content;
        }
//        String contentNew = TextMatcher.findClosestText(textContent, content);
        try {
            FuzzyTextMatcherUtils.MatchResult result = FuzzyTextMatcherUtils.findBestMatch(textContent, content);
            log.info("根据模型提取到的原文内容，获取真正的原文内容：{}", result.matchedText);
            if (StringUtils.isNotBlank(result.matchedText)) {
                return result.matchedText;
            }
            log.info("根据模型提取到的原文内容，是否正确：{}", textContent.contains(result.matchedText));
        } catch (Exception e) {
        }
        return content;
    }

    private String docRewrite(String original, String rewriteStyle) {
        log.info("确认改写，original：{}，rewriteStyle：{}", original, rewriteStyle);
        if (Objects.isNull(original) || StringUtils.isEmpty(rewriteStyle)) {
            return null;
        }

        String originalMd = markdownParsingUtils.convertHtmlToMarkdown(original);
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CHAT_REWRITE, rewriteStyle, originalMd);
        ChatCompletionResponseDTO chatCompletionResponseDTO = editService.chatCompletions(promptWordsConfig);
        if (Objects.isNull(chatCompletionResponseDTO) || CollectionUtils.isEmpty(chatCompletionResponseDTO.getChoices()) || Objects.isNull(chatCompletionResponseDTO.getChoices().get(0).getMessage())) {
            return null;
        }
        String content = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
        log.info("确认改写，改写后内容content：{}", content);
        return content;
    }

}