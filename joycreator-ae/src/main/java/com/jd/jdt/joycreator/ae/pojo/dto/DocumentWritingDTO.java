package com.jd.jdt.joycreator.ae.pojo.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * Edit文档写作请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
public class DocumentWritingDTO {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 消息列表
     */
    private List<DoronChatMessageDTO> messages;

    /**
     * 写作大纲
     */
    private CreateOutlineDTO createOutlineDTO;

    /**
     * 工具参数
     */
    private Object toolsParam;
}
