package com.jd.jdt.joycreator.ae.enums;

/**
 * <p>
 * 输出类型枚举
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-04-27
 */
public enum ChatRoleEnum {

    USER("USER", "用户输入"),
    SYSTEM("SYST<PERSON>", "系统输出"),
    ASSISTANT("ASSISTANT", "模型输出"),
    TEMPLATE_LIBRARY_QUERY("TEMPLATE_LIBRARY_QUERY", "开始调用模版库检索工具"),
    TEMPLATE_LIBRARY("TEMPLATE_LIBRARY", "模版库"),
    DOCCONTENT_REWRITE("DOCCONTENT_REWRITE", "原文改写/重写/优化/续写等"),
    DOCCONTENT_EXTRACTION_QUERY("DOCCONTENT_EXTRACTION_QUERY", "开始调用提取原文工具"),
    DOCCONTENT_EXTRACTION("DOCCONTENT_EXTRACTION", "原文提取/抽取"),
    FREELANCE_WRITING("FREELANCE_WRITING", "自由写作/撰写"),
    FREELANCE_WRITING_START("FREELANCE_WRITING_START", "开始调用自由撰写工具"),
    OUTLINE("OUTLINE", "大纲数据体"),
    TOOL("TOOL", "工具"),
    TEXT_REPLACEMENT("TEXT_REPLACEMENT", "原文替换"),
    TEXT_REWRITE("TEXT_REWRITE", "文档改写"),
    DOCCONTENT_EXTRACTION_ERROR("DOCCONTENT_EXTRACTION_ERROR", "原文提取错误"),
    TEXT("TEXT", "文本"),
    FILE("FILE", "文件"),
    EXCERPT("EXCERPT", "原文引用"),
    AUTOFILL("AUTOFILL", "开始调用多任务识别工具"),
    DOCUMENT("DOCUMENT", "文档正文"),
    TASK_FEEDBACK("TASK_FEEDBACK", "任务结果反馈"),
    ;

    /**
     * 源文件类型
     */
    private String Type;

    /**
     * 描述
     */
    private String desc;


    ChatRoleEnum(String type, String desc) {
        this.Type = type;
        this.desc = desc;
    }

    public static ChatRoleEnum getEnum(String type) {
        for (ChatRoleEnum a : ChatRoleEnum.values()) {
            if (a.getType().equals(type)) {
                return a;
            }
        }
        return null;
    }

    public String getType() {
        return Type;
    }

    public void setType(String type) {
        Type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
