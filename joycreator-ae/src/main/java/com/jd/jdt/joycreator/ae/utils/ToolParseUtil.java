package com.jd.jdt.joycreator.ae.utils;

import com.jd.jdt.joycreator.ae.enums.ToolLabelsEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.ToolDtlDTO;
import com.jd.jsf.gd.util.JsonUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 工具解析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public class ToolParseUtil {

    private static final Pattern TOOL_CODE_PATTERN = Pattern.compile("```xml(.*?)```", Pattern.DOTALL);

    /**
     * 解析单个工具的XML结构
     *
     * @param text XML文本内容
     * @return 工具详情DTO
     */
    public static ToolDtlDTO parse(String text) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }

        String contentToParse;

        Matcher markdownMatcher = TOOL_CODE_PATTERN.matcher(text);
        if (markdownMatcher.find()) {
            contentToParse = markdownMatcher.group(1).trim();
        } else {
            // Fallback: try to extract content that looks like XML from the string.
            int firstOpen = text.indexOf('<');
            int lastClose = text.lastIndexOf('>');
            if (firstOpen != -1 && lastClose > firstOpen) {
                contentToParse = text.substring(firstOpen, lastClose + 1).trim();
            } else {
                return null;
            }
        }

        if (contentToParse.isEmpty()) {
            return null;
        }

        try {
            return parseXmlToDto(contentToParse);
        } catch (Exception e) {
            e.printStackTrace(); // In a real application, use a logger
            return null;
        }
    }

    /**
     * 解析包含多个工具的XML结构
     *
     * @param text XML文本内容
     * @return 工具详情DTO列表
     */
    public static List<ToolDtlDTO> parseTools(String text) {
        if (text == null || text.trim().isEmpty()) {
            return new ArrayList<>();
        }

        String contentToParse;

        Matcher markdownMatcher = TOOL_CODE_PATTERN.matcher(text);
        if (markdownMatcher.find()) {
            contentToParse = markdownMatcher.group(1).trim();
        } else {
            // Fallback: try to extract content that looks like XML from the string.
            int firstOpen = text.indexOf('<');
            int lastClose = text.lastIndexOf('>');
            if (firstOpen != -1 && lastClose > firstOpen) {
                contentToParse = text.substring(firstOpen, lastClose + 1).trim();
            } else {
                return new ArrayList<>();
            }
        }

        if (contentToParse.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            return parseMultipleXmlToDto(contentToParse);
        } catch (Exception e) {
            e.printStackTrace(); // In a real application, use a logger
            return new ArrayList<>();
        }
    }

    private static ToolDtlDTO parseXmlToDto(String xmlContent) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document doc = builder.parse(new InputSource(new StringReader(xmlContent)));
        doc.getDocumentElement().normalize();

        Element rootElement = doc.getDocumentElement();

        ToolDtlDTO dto = new ToolDtlDTO();
        ToolLabelsEnum toolLabel = ToolLabelsEnum.getEnumByCode(rootElement.getNodeName());
        dto.setToolLabels(toolLabel);

        Map<String, Object> params = new HashMap<>();
        NodeList childNodes = rootElement.getChildNodes();
        for (int i = 0; i < childNodes.getLength(); i++) {
            Node node = childNodes.item(i);
            if (node.getNodeType() == Node.ELEMENT_NODE) {
                params.put(node.getNodeName(), getNodeValue(node));
            }
        }
        dto.setToolParams(params);

        return dto;
    }

    /**
     * 解析包含多个工具的XML内容
     *
     * @param xmlContent XML内容
     * @return 工具详情DTO列表
     * @throws Exception 解析异常
     */
    private static List<ToolDtlDTO> parseMultipleXmlToDto(String xmlContent) throws Exception {
        List<ToolDtlDTO> tools = new ArrayList<>();
        
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document doc = builder.parse(new InputSource(new StringReader(xmlContent)));
        doc.getDocumentElement().normalize();

        Element rootElement = doc.getDocumentElement();
        if (!"tools".equals(rootElement.getNodeName())) {
            // 如果根节点不是tools，尝试作为单个工具解析
            ToolDtlDTO singleTool = parseXmlToDto(xmlContent);
            if (singleTool != null) {
                tools.add(singleTool);
            }
            return tools;
        }

        // 解析tools标签下的每个工具
        NodeList toolNodes = rootElement.getChildNodes();
        for (int i = 0; i < toolNodes.getLength(); i++) {
            Node node = toolNodes.item(i);
            if (node.getNodeType() == Node.ELEMENT_NODE) {
                Element toolElement = (Element) node;
                ToolDtlDTO dto = new ToolDtlDTO();
                
                // 设置工具类型
                ToolLabelsEnum toolLabel = ToolLabelsEnum.getEnumByCode(toolElement.getNodeName());
                dto.setToolLabels(toolLabel);

                // 解析工具参数
                Map<String, Object> params = new HashMap<>();
                NodeList paramNodes = toolElement.getChildNodes();
                for (int j = 0; j < paramNodes.getLength(); j++) {
                    Node paramNode = paramNodes.item(j);
                    if (paramNode.getNodeType() == Node.ELEMENT_NODE) {
                        params.put(paramNode.getNodeName(), getNodeValue(paramNode));
                    }
                }
                dto.setToolParams(params);
                
                tools.add(dto);
            }
        }

        return tools;
    }

    private static Object getNodeValue(Node node) {
        if (!node.hasChildNodes()) {
            return node.getTextContent();
        }

        NodeList children = node.getChildNodes();
        if (children.getLength() == 1 && children.item(0).getNodeType() == Node.TEXT_NODE) {
            return children.item(0).getTextContent().trim();
        }

        List<Object> childValues = new ArrayList<>();
        Map<String, Object> childMap = new HashMap<>();
        boolean hasElements = false;

        for (int i = 0; i < children.getLength(); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                hasElements = true;
                Object value = getNodeValue(child);
                if (childMap.containsKey(child.getNodeName())) {
                    Object existing = childMap.get(child.getNodeName());
                    if (existing instanceof List) {
                        ((List<Object>) existing).add(value);
                    } else {
                        List<Object> list = new ArrayList<>();
                        list.add(existing);
                        list.add(value);
                        childMap.put(child.getNodeName(), list);
                    }
                } else {
                    childMap.put(child.getNodeName(), value);
                }
            }
        }
        if (hasElements) {
            return childMap;
        }
        return node.getTextContent().trim();
    }

    public static void main(String[] args) {
        // 测试用例1：正常的XML格式
        String testCase1 = "<tools>\n" +
                "<doccontentRewrite>\n" +
                "<doccontent_rewrite_keyword>Java介绍</doccontent_rewrite_keyword>\n" +
                "<doccontent_rewrite_style>使内容更专业、简洁</doccontent_rewrite_style>\n" +
                "</doccontentRewrite>\n" +
                "<textReplacement>\n" +
                "<text_replacement_command>将标题改为Java入门</text_replacement_command>\n" +
                "</textReplacement>\n" +
                "</tools>";

        System.out.println("测试用例1 - 正常XML格式：");
        List<ToolDtlDTO> result1 = parseTools(testCase1);
        System.out.println("工具数量：" + result1.size());
        for (ToolDtlDTO tool : result1) {
            System.out.println("工具类型：" + tool.getToolLabels());
            System.out.println("工具参数：" + JsonUtils.toJSONString(tool.getToolParams()));
        }

        // 测试用例2：Markdown格式的XML
        String testCase2 = "```xml\n" + testCase1 + "\n```";
        System.out.println("\n测试用例2 - Markdown格式：");
        List<ToolDtlDTO> result2 = parseTools(testCase2);
        System.out.println("工具数量：" + result2.size());
        for (ToolDtlDTO tool : result2) {
            System.out.println("工具类型：" + tool.getToolLabels());
            System.out.println("工具参数：" + JsonUtils.toJSONString(tool.getToolParams()));
        }

        // 测试用例3：错误格式测试
        String testCase3 = "<invalid>test</invalid>";
        System.out.println("\n测试用例3 - 错误格式：");
        List<ToolDtlDTO> result3 = parseTools(testCase3);
        System.out.println("工具数量：" + result3.size());

        // 测试用例4：空内容测试
        String testCase4 = "";
        System.out.println("\n测试用例4 - 空内容：");
        List<ToolDtlDTO> result4 = parseTools(testCase4);
        System.out.println("工具数量：" + result4.size());
    }
}