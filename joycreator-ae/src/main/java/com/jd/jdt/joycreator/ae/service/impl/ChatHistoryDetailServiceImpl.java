package com.jd.jdt.joycreator.ae.service.impl;

import com.google.common.collect.Lists;
import com.jd.jdt.joycreator.ae.dao.plus.*;
import com.jd.jdt.joycreator.ae.entity.*;
import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryDetailDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryOperationDTO;
import com.jd.jdt.joycreator.ae.pojo.dto.ChatHistoryOperationExpandDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.*;
import com.jd.jdt.joycreator.ae.service.ChatHistoryDetailService;
import com.jd.jdt.joycreator.ae.service.ChatHistoryDetailTemplateService;
import com.jd.jdt.joycreator.ae.utils.JoyCreatorUtil;
import com.jd.jsf.gd.util.JsonUtils;
import com.jd.jsf.gd.util.StringUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Queue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <p>
 * 聊天记录明细 服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Log4j2
@Service
public class ChatHistoryDetailServiceImpl implements ChatHistoryDetailService {

    @Autowired
    private IChatHistoryMapper iChatHistoryMapper;

    @Autowired
    private IChatHistoryDetailMapper iChatHistoryDetailMapper;

    @Autowired
    private IChatHistoryDetailTemplateMapper iChatHistoryDetailTemplateMapper;

    @Autowired
    private ChatHistoryDetailTemplateService chatHistoryDetailTemplateService;

    @Autowired
    private ITemplateLibraryMapper iTemplateLibraryMapper;

    @Autowired
    private JoyCreatorUtil joyCreatorUtil;

    @Autowired
    private IFileMapper iFileMapper;

    @Autowired
    private IChatHistoryDetailExcerptMapper iChatHistoryDetailExcerptMapper;


    @Override
    public void saveBatch(Queue<ChatHistoryDetailDTO> chatHistoryQueue, AtomicBoolean done, String sessionId) {
        log.info("保存聊天记录，开始执行，sessionId：{}", sessionId);
        new Thread(() -> {
            if (StringUtils.isEmpty(sessionId)) {
                return;
            }
            ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(sessionId);
            log.info("保存聊天记录，开始执行，chatHistory：{}", JsonUtils.toJSONString(chatHistory));
            if (Objects.isNull(chatHistory)) {
                return;
            }
            long startTime = System.currentTimeMillis();
            long timeout = 300000;
            while (true) {
                if (System.currentTimeMillis() - startTime > timeout) {
                    log.warn("保存聊天记录，执行超时，done：{}", done.get());
                    break;
                }
                ChatHistoryDetailDTO chatHistoryDetailDTO = chatHistoryQueue.poll();
                if (done.get() && Objects.isNull(chatHistoryDetailDTO)) {
                    break;
                }
                if (Objects.isNull(chatHistoryDetailDTO)) {
                    continue;
                }

                ChatHistoryDetail chatHistoryDetail = new ChatHistoryDetail();
                BeanUtils.copyProperties(chatHistoryDetailDTO, chatHistoryDetail);
                chatHistoryDetail.setChatHistoryId(chatHistory.getId());
                boolean saveChatHistoryDetail = iChatHistoryDetailMapper.save(chatHistoryDetail);
                List<ChatHistoryDetailTemplate> chatHistoryDetailTemplateList = chatHistoryDetailDTO.getChatHistoryDetailTemplateList();
                List<ChatHistoryDetailExcerpt> chatHistoryDetailExcerptList = chatHistoryDetailDTO.getChatHistoryDetailExcerptList();
                if (saveChatHistoryDetail) {
                    if (CollectionUtils.isNotEmpty(chatHistoryDetailTemplateList)) {
                        chatHistoryDetailTemplateList.forEach(c -> c.setChatHistoryDetailId(chatHistoryDetail.getId()));
                        iChatHistoryDetailTemplateMapper.saveBatch(chatHistoryDetailTemplateList);
                    }
                    if (CollectionUtils.isNotEmpty(chatHistoryDetailExcerptList)) {
                        chatHistoryDetailExcerptList.forEach(c -> c.setChatHistoryDetailId(chatHistoryDetail.getId()));
                        iChatHistoryDetailExcerptMapper.saveBatch(chatHistoryDetailExcerptList);
                    }
                }
                log.info("保存聊天记录，执行完成，done：{}，chatHistoryDetail：{}", done.get(), JsonUtils.toJSONString(chatHistoryDetail));
            }
        }).start();
    }

    @Override
    public ChatHistoryVO dtl(String sessionId) {
        ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(sessionId);
        if (Objects.isNull(chatHistory)) {
            return null;
        }
        ChatHistoryVO chatHistoryVO = new ChatHistoryVO();
        BeanUtils.copyProperties(chatHistory, chatHistoryVO);
        List<ChatHistoryDetail> chatHistoryDetailList = iChatHistoryDetailMapper.listChatHistoryDetailBySessionId(chatHistory.getId());
        if (CollectionUtils.isEmpty(chatHistoryDetailList)) {
            return chatHistoryVO;
        }
        chatHistoryDetailList = chatHistoryDetailList.stream().filter(c -> ChatRoleEnum.AUTOFILL != c.getChatRole() && ChatRoleEnum.TOOL != c.getChatRole()).collect(Collectors.toList());
        List<ChatHistoryDetailVO> chatHistoryDetailVOList = Lists.newArrayList();
        List<ChatHistoryDetailVO> assistantContentList = Lists.newArrayList();

        for (int i = 0; i < chatHistoryDetailList.size(); i++) {
            ChatHistoryDetail chatHistoryDetail = chatHistoryDetailList.get(i);

            // 处理USER角色 - 保持现有逻辑
            if (ChatRoleEnum.USER == chatHistoryDetail.getChatRole()) {
                // 如果之前有ASSISTANT相关内容，先处理完
                if (CollectionUtils.isNotEmpty(assistantContentList)) {
                    ChatHistoryDetailVO assistantVO = createAssistantVO(assistantContentList);
                    chatHistoryDetailVOList.add(assistantVO);
                    assistantContentList.clear();
                }

                // 处理USER角色的逻辑保持不变
                List<ChatHistoryDetailExcerptVO> userMainList = Lists.newArrayList();
                List<ChatHistoryDetailExcerpt> chatHistoryDetailExcerptList = iChatHistoryDetailExcerptMapper.listByChatHistoryDetailId(chatHistoryDetail.getId());
                ChatHistoryDetailVO chatHistoryDetailUserVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailUserVO);
                if (CollectionUtils.isNotEmpty(chatHistoryDetailExcerptList)) {
                    List<ChatHistoryDetailExcerptVO> userExcerptList = chatHistoryDetailExcerptList.stream().map(c -> {
                        ChatHistoryDetailExcerptVO chde = new ChatHistoryDetailExcerptVO();
                        BeanUtils.copyProperties(c, chde);
                        if (c.getMsgType() == ChatRoleEnum.FILE) {
                            File file = iFileMapper.getById(c.getFileId());
                            if (Objects.nonNull(file)) {
                                FileVO fileVO = new FileVO();
                                BeanUtils.copyProperties(file, fileVO);
                                fileVO.setLanUrl(joyCreatorUtil.inteProtocol(file.getLanUrl()));
                                fileVO.setWanUrl(joyCreatorUtil.inteProtocol(file.getWanUrl()));
                                chde.setFileVO(fileVO);
                            }
                        }
                        return chde;
                    }).collect(Collectors.toList());
                    userMainList.addAll(userExcerptList);

                    boolean noneMatch = chatHistoryDetailExcerptList.stream().noneMatch(c -> ChatRoleEnum.TEXT == c.getMsgType());
                    if (noneMatch) {
                        ChatHistoryDetailExcerptVO chde = new ChatHistoryDetailExcerptVO();
                        chde.setMsgType(ChatRoleEnum.TEXT);
                        chde.setChatHistoryDetailId(chatHistoryDetail.getChatHistoryId());
                        chde.setChatContent(chatHistoryDetail.getChatContent());
                        userMainList.add(chde);
                    }
                } else {
                    ChatHistoryDetailExcerptVO chde = new ChatHistoryDetailExcerptVO();
                    chde.setMsgType(ChatRoleEnum.TEXT);
                    chde.setChatHistoryDetailId(chatHistoryDetail.getChatHistoryId());
                    chde.setChatContent(chatHistoryDetail.getChatContent());
                    userMainList.add(chde);
                }
                chatHistoryDetailUserVO.setChatContent(userMainList);
                chatHistoryDetailVOList.add(chatHistoryDetailUserVO);
            }
            // 处理ASSISTANT角色
            else if (ChatRoleEnum.ASSISTANT == chatHistoryDetail.getChatRole()) {
                ChatHistoryDetailVO chatHistoryDetailVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, chatHistoryDetailVO);
                assistantContentList.add(chatHistoryDetailVO);
            }
            // 处理TASK_FEEDBACK角色：只添加当前数据，前一条数据已经在之前的循环中处理过了
            else if (ChatRoleEnum.TASK_FEEDBACK == chatHistoryDetail.getChatRole()) {
                // 只添加当前TASK_FEEDBACK数据，不重复添加前一条数据
                ChatHistoryDetailVO currentVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, currentVO);
                assistantContentList.add(currentVO);
            }
            // 处理其他角色：TASK_FEEDBACK上边到USER后的所有角色
            else {
                // 如果不是USER、ASSISTANT、TASK_FEEDBACK角色，也添加到assistantContentList中
                ChatHistoryDetailVO otherVO = new ChatHistoryDetailVO();
                BeanUtils.copyProperties(chatHistoryDetail, otherVO);
                if (ChatRoleEnum.TEXT_REWRITE == chatHistoryDetail.getChatRole() || ChatRoleEnum.DOCCONTENT_REWRITE == chatHistoryDetail.getChatRole()) {
                    AutofillExtractionVO autofillExtractionVO = JsonUtils.parseObject(chatHistoryDetail.getChatContent(), AutofillExtractionVO.class);
                    otherVO.setChatContent(autofillExtractionVO);
                } else if (ChatRoleEnum.OUTLINE == chatHistoryDetail.getChatRole()) {
                                    String chatContent = chatHistoryDetail.getChatContent();
                                    log.info("开始解析大纲JSON，原始内容: {}", chatContent);
                                    try {
                                        // 验证JSON格式
                                        if (chatContent == null || chatContent.trim().isEmpty()) {
                                            log.error("大纲内容为空");
                                            return null;
                                        }
                                        // 检查是否是合法的JSON格式
                                        if (!chatContent.trim().startsWith("{")) {
                                            log.error("大纲内容不是合法的JSON对象格式，内容: {}", chatContent);
                                            // 尝试处理可能的转义问题
                                            chatContent = chatContent.replace("\\\"", "\"").trim();
                                            if (chatContent.startsWith("\"") && chatContent.endsWith("\"")) {
                                                chatContent = chatContent.substring(1, chatContent.length() - 1);
                                            }
                                        }
                                        log.info("处理后的JSON内容: {}", chatContent);
                                        CreateOutlineVO createOutlineVO = JsonUtils.parseObject(chatContent, CreateOutlineVO.class);
                                        log.info("JSON解析成功，解析结果: {}", createOutlineVO);
                                        otherVO.setChatContent(createOutlineVO);
                                    } catch (Exception e) {
                                        log.error("解析大纲JSON失败，错误信息: {}, 原始内容: {}", e.getMessage(), chatContent);
                                        throw e;
                                    }
                                }
                assistantContentList.add(otherVO);
            }
        }

        // 处理最后剩余的ASSISTANT内容
        if (CollectionUtils.isNotEmpty(assistantContentList)) {
            ChatHistoryDetailVO assistantVO = createAssistantVO(assistantContentList);
            chatHistoryDetailVOList.add(assistantVO);
        }

        chatHistoryVO.setChatHistoryDetailVOList(chatHistoryDetailVOList);
        return chatHistoryVO;
    }

    /**
     * 创建ASSISTANT角色的VO对象
     */
    private ChatHistoryDetailVO createAssistantVO(List<ChatHistoryDetailVO> assistantContentList) {
        ChatHistoryDetailVO assistantVO = new ChatHistoryDetailVO();
        assistantVO.setChatRole(ChatRoleEnum.ASSISTANT);

        // 从内容列表中获取第一个ASSISTANT角色的信息作为主要信息
        for (ChatHistoryDetailVO detailVO : assistantContentList) {
            if (ChatRoleEnum.ASSISTANT == detailVO.getChatRole()) {
                assistantVO.setBusinessNo(detailVO.getBusinessNo());
                assistantVO.setChatHistoryId(detailVO.getChatHistoryId());
                break;
            }
        }

        // 如果没有找到ASSISTANT角色，使用第一个元素的信息
        if (assistantVO.getBusinessNo() == null && CollectionUtils.isNotEmpty(assistantContentList)) {
            ChatHistoryDetailVO firstDetail = assistantContentList.get(0);
            assistantVO.setBusinessNo(firstDetail.getBusinessNo());
            assistantVO.setChatHistoryId(firstDetail.getChatHistoryId());
        }

        // 将所有内容设置为chatContent
        assistantVO.setChatContent(Lists.newArrayList(assistantContentList));
        return assistantVO;
    }

    @Override
    public Boolean confirmDoccontentRewrite(ChatHistoryOperationDTO chatHistoryOperationDTO) {
        return iChatHistoryDetailMapper.confirmDoccontentRewriteBybusinessNo(chatHistoryOperationDTO.getBusinessNo());
    }

    @Override
    public Boolean updateOutline(ChatHistoryOperationExpandDTO chatHistoryOperationOutlineDTO) {
        return iChatHistoryDetailMapper.updateChatContent(chatHistoryOperationOutlineDTO.getBusinessNo(), JsonUtils.toJSONString(chatHistoryOperationOutlineDTO.getCreateOutlineVO()));
    }

    @Override
    public void saveAsync(AtomicBoolean done, StringBuffer contentBufferAll, ChatRoleEnum chatRoleEnum, String sessionId, String businessNo) {
        new Thread(() -> {
            while (!done.get()) {
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                }
            }
            ChatHistory chatHistory = iChatHistoryMapper.chatHistoryBySessionId(sessionId);
            ChatHistoryDetail chatHistoryDetail = new ChatHistoryDetail();
            chatHistoryDetail.setChatHistoryId(chatHistory.getId());
            chatHistoryDetail.setChatRole(chatRoleEnum);
            chatHistoryDetail.setChatContent(contentBufferAll.toString());
            chatHistoryDetail.setBusinessNo(businessNo);
            iChatHistoryDetailMapper.save(chatHistoryDetail);
        }).start();
    }

}
