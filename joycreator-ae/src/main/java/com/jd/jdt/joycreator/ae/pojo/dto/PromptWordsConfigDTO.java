package com.jd.jdt.joycreator.ae.pojo.dto;

import com.jd.jdt.joycreator.ae.enums.PromptCodeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 提示词管理DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26
 */
@Data
public class PromptWordsConfigDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 提示词编码
     */
    private PromptCodeEnum promptWordCode;

    /**
     * 模型
     */
    private String model;

    /**
     * 提示词来源
     */
    private String promptSource;

    /**
     * 提示词
     */
    private String promptWord;

    /**
     * 描述/说明
     */
    private String description;

    /**
     * 采样温度
     */
    private BigDecimal temperature;

    /**
     * 核取样
     */
    private BigDecimal topK;

    /**
     * 输出最大token数
     */
    private BigDecimal maxTokens;

    /**
     * 用户历史会话记录
     */
    private List<DoronChatMessageDetailDTO> doronChatMessageDetailDTOList;



}
