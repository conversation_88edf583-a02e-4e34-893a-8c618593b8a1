package com.jd.jdt.joycreator.ae.enums;

/**
 * <p>
 * 工具标签枚举
 * </p>
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @since 2025-04-27
 */
public enum ToolLabelsEnum {

    TEMPLATE_LIBRARY("<template_library>", "使用模版库检索工具"),
    DOCCONTENT_REWRITE("<doccontentRewrite>", "使用重写/改写工具"),
    DOCCONTENT_EXTRACTION("<doccontentExtraction>", "使用原文提取工具"),
    FREELANCE_WRITING("<freelanceWriting>", "使用自由撰写工具"),
    TEXT_REPLACEMENT("<textReplacement>", "使用替换工具"),
    ;

    /**
     * 源文件类型
     */
    private String Type;

    /**
     * 描述
     */
    private String desc;


    ToolLabelsEnum(String type, String desc) {
        this.Type = type;
        this.desc = desc;
    }

    public static ToolLabelsEnum getEnumByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (ToolLabelsEnum value : values()) {
            // Remove "<" and ">" from the type before comparing
            String type = value.getType().replaceAll("[<>/]", "");
            if (type.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getType() {
        return Type;
    }

    public void setType(String type) {
        Type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
