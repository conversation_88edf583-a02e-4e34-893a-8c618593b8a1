package com.jd.jdt.joycreator.ae;

import com.jd.jdt.joybuilder.permission.entity.ResponseResult;
import com.jd.jdt.joycreator.ae.enums.AsposeConvertTypeEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.AsposeConvertDTO;
import com.jd.jdt.joycreator.ae.pojo.vo.AsposeConvertVO;
import com.jd.jdt.joycreator.ae.rpc.feign.AsposeConvertService;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class OfficeConvertFeignClientTest {

    @Autowired
    private AsposeConvertService asposeConvertFeignService;

    private static final String TEST_FILE_PATH = "src/main/resources/scripts/盖章(水印版)_1753691673240.docx";
    private static final String OUTPUT_DIR = "src/main/resources/scripts/";

    @Test
    public void testOfficeConvert() throws IOException {
        // 准备测试文件
        File inputFile = new File(TEST_FILE_PATH);
        assertTrue(inputFile.exists(), "测试文件不存在");
        byte[] fileContent = Files.readAllBytes(inputFile.toPath());

        // 构建请求参数
        AsposeConvertDTO convertDTO = new AsposeConvertDTO();
        convertDTO.setFileName(inputFile.getName());
        convertDTO.setByteArray(fileContent);
        convertDTO.setAsposeConvertType(AsposeConvertTypeEnum.DOCX_TO_PDF);

        // 调用接口
        ResponseResult<AsposeConvertVO> response = asposeConvertFeignService.asposeConvert(convertDTO);

        // 验证响应
        assertNotNull(response, "响应不能为空");
        assertEquals(200, response.getCode(), "响应状态码应为200");
        assertNotNull(response.getData(), "响应数据不能为空");
        
        // 保存转换后的文件
        AsposeConvertVO result = response.getData();
        String outputFileName = System.currentTimeMillis() + AsposeConvertTypeEnum.DOCX_TO_PDF.getTargetSuffix();
        Path outputPath = Paths.get(OUTPUT_DIR, outputFileName);
        FileUtils.writeByteArrayToFile(outputPath.toFile(), result.getByteArray());
        
        assertTrue(Files.exists(outputPath), "输出文件应该存在");
        assertTrue(Files.size(outputPath) > 0, "输出文件不应该为空");
    }
}