# Aspose OCR Demo 使用说明

## 概述

本目录包含了基于Aspose OCR库的PDF文字识别演示代码，可以对PDF文件进行OCR识别并将结果输出到指定目录。

## 文件结构

```
joycreator-ae/src/main/test/java/com/jd/jdt/joycreator/ae/ocr/
├── AsposeOcrDemoTest.java    # 主要的OCR测试类
└── README.md                 # 本说明文档
```

## 功能特性

- ✅ **PDF文字识别**: 支持对PDF文件进行OCR文字识别
- ✅ **中英文支持**: 支持中文、英文及混合文本识别
- ✅ **自动优化**: 包含自动倾斜校正和对比度校正
- ✅ **结果输出**: 自动将识别结果保存为文本文件
- ✅ **详细日志**: 提供完整的处理过程日志
- ✅ **错误处理**: 完善的异常处理机制

## 测试物料

- **输入文件**: `joycreator-ae/src/main/resources/scripts/1754053748966.pdf`
- **输出目录**: `joycreator-ae/src/main/resources/scripts/`
- **输出文件**: `ocr_result_yyyyMMdd_HHmmss.txt` (自动生成时间戳)

## 使用方法

### 1. 运行完整OCR测试

```bash
# 在项目根目录执行
mvn test -Dtest=AsposeOcrDemoTest#testPdfOcrRecognition
```

### 2. 运行基础功能测试

```bash
# 测试OCR引擎基本功能
mvn test -Dtest=AsposeOcrDemoTest#testOcrEngineBasicFunction
```

### 3. 在IDE中运行

1. 打开 `AsposeOcrDemoTest.java` 文件
2. 右键点击 `testPdfOcrRecognition()` 方法
3. 选择 "Run testPdfOcrRecognition()"

## 核心代码说明

### 许可证激活

```java
@BeforeAll
public static void initializeLicense() {
    // 使用内置许可证激活Aspose OCR
    com.aspose.ocr.License ocrLicense = new com.aspose.ocr.License();
    ocrLicense.setLicense(new ByteArrayInputStream(licenseBytes));
}
```

### OCR识别配置

```java
RecognitionSettings settings = new RecognitionSettings();
settings.setLanguage(com.aspose.ocr.Language.Chi);  // 设置中文识别
settings.setAutoSkew(true);                         // 启用自动倾斜校正
settings.setAutoContrast(true);                     // 启用自动对比度校正
```

### 执行识别

```java
AsposeOCR ocrEngine = new AsposeOCR();
RecognitionResult result = ocrEngine.RecognizePdf(INPUT_PDF_PATH, settings);
String recognizedText = result.recognitionText;
```

## 输出文件格式

识别结果文件包含以下信息：

```
================================================================================
Aspose OCR 识别结果
================================================================================
输入文件: joycreator-ae/src/main/resources/scripts/1754053748966.pdf
识别时间: 2025-01-08 21:13:00
文本长度: 1234 字符
================================================================================

[这里是识别出的文本内容]

================================================================================
OCR识别完成
================================================================================
```

## 依赖要求

### Maven依赖

确保项目中包含以下依赖：

```xml
<dependency>
    <groupId>com.aspose</groupId>
    <artifactId>aspose-ocr</artifactId>
    <version>25.5.0</version>
</dependency>
```

### JAR文件

项目中已包含：
- `joycreator-ae/src/main/resources/static/aspose-ocr-25.5.0.jar`

## 配置选项

### 语言设置

```java
// 中文识别
settings.setLanguage(com.aspose.ocr.Language.Chi);

// 英文识别  
settings.setLanguage(com.aspose.ocr.Language.Eng);

// 自动检测
settings.setLanguage(com.aspose.ocr.Language.None);
```

### 识别区域设置

```java
// 设置特定识别区域（可选）
settings.setRecognitionAreas(Arrays.asList(
    new Rectangle(0, 0, 500, 300)
));
```

## 常见问题

### Q1: 许可证激活失败怎么办？

**A**: 检查许可证内容是否完整，确保网络连接正常。如果问题持续，请检查Aspose OCR版本兼容性。

### Q2: OCR识别结果为空？

**A**: 可能原因：
- PDF文件是纯图片格式，文字不清晰
- 选择的识别语言不匹配
- PDF文件损坏或格式不支持

**解决方案**：
- 尝试不同的语言设置
- 检查PDF文件质量
- 启用自动对比度和倾斜校正

### Q3: 识别速度较慢？

**A**: OCR识别是计算密集型操作，处理时间取决于：
- PDF文件大小和页数
- 图片分辨率
- 文字复杂程度
- 系统性能

### Q4: 如何提高识别准确率？

**A**: 
- 确保PDF文件清晰度高
- 选择正确的识别语言
- 启用自动校正功能
- 对于特定区域，可以设置识别区域

## 扩展功能

### 批量处理

可以扩展代码支持批量处理多个PDF文件：

```java
public void batchProcessPdfs(List<String> pdfPaths) {
    for (String pdfPath : pdfPaths) {
        // 处理每个PDF文件
    }
}
```

### 自定义输出格式

可以扩展输出格式，支持JSON、XML等：

```java
public void saveAsJson(String recognizedText, String outputPath) {
    // 保存为JSON格式
}
```

## 技术支持

如有问题，请参考：
1. Aspose OCR官方文档
2. 项目中的参考代码：`FileToPdfConverter.java`
3. 使用说明：`joycreator-ae/src/main/resources/static/使用说明.txt`

---

**创建时间**: 2025-01-08  
**版本**: 1.0  
**作者**: JoyCode Team