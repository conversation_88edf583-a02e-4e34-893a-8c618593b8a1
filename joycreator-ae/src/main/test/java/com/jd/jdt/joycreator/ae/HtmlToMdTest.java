package com.jd.jdt.joycreator.ae;

import com.jd.jdt.joycreator.ae.dao.plus.ITemplateLibraryMapper;
import com.jd.jdt.joycreator.ae.entity.TemplateLibrary;
import com.jd.jdt.joycreator.ae.service.ChatStudioService;
import com.jd.jdt.joycreator.ae.utils.MarkdownParsingUtils;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.html2md.converter.FlexmarkHtmlConverter;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.util.ast.Node;
import com.vladsch.flexmark.util.data.MutableDataSet;
import lombok.extern.log4j.Log4j2;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@Log4j2
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class HtmlToMdTest {

    @Autowired
    private ITemplateLibraryMapper iTemplateLibraryMapper;
    @Autowired
    private MarkdownParsingUtils markdownParsingUtils;
    @Autowired
    private ChatStudioService editService;

    @Test
    public void videosResult2() {
        TemplateLibrary templateLibrary = iTemplateLibraryMapper.getById(953780042903498753L);
        String markdown = markdownParsingUtils.convertHtmlToMarkdown(templateLibrary.getContent());
        String promptWords =
                "**角色描述**：您是一位专业的文档内容提取助手，专注于从原文中根据用户的具体意图精确提取指定内容。您的目标是确保提取的内容保持完整性和准确性。\n" +
                        "\n" +
                        "**任务说明**：\n" +
                        "1. **用户请求示例**：修改“xxxx”部分的内容。\n" +
                        "2. **输出要求**：完整无缺地输出“xxxx”部分的原文内容，包括所有格式、空格、大小写和标点符号。请勿进行任何形式的修改或删除，也不要对输出的内容做任何的描述。\n" +
                        "\n" +
                        "**操作步骤**：\n" +
                        "- 仔细阅读用户提供的意图描述，以确保对提取要求的准确理解。\n" +
                        "- 在原文中定位与用户意图相符的部分。\n" +
                        "- 提取并输出该部分内容，确保保持原文的格式和细节。\n" +
                        "\n" +
                        "**注意事项**：\n" +
                        "- 确保提取内容的完整性，避免遗漏任何细节。\n" +
                        "- 优先提取最接近的相关内容。\n" +
                        "\n" +
                        "**用户意图**：%s\n" +
                        "\n" +
                        "**原文内容**：\n" +
                        "%s\n" +
                        "\n";
//        ChatCompletionResponseDTO chatCompletionResponseDTO = editService.chatCompletions(String.format(promptWords, "帮我修改一下2.2.2 甘特图的内容", markdown), "anthropic.claude-3-5-sonnet-20241022-v2:0");


//        String markdown2 = markdownParsingUtils.convertHtmlToMarkdown2(templateLibrary.getContent());
//        String markdown3 = markdownParsingUtils.convertHtmlToMarkdown3(templateLibrary.getContent());
        System.out.println(markdown);
//        System.out.println(markdown2);
//        System.err.println(markdown3);


        MutableDataSet options = new MutableDataSet();
        Parser parser = Parser.builder(options).build();
        HtmlRenderer renderer = HtmlRenderer.builder(options).build();
        Node document = parser.parse(templateLibrary.getContent());
        String markdown4 = renderer.render(document);
        System.err.println(markdown4);

        String markdown5 = convert(templateLibrary.getContent());
        System.err.println(markdown5);

    }

    /*public static String convert(String html) {
        // 创建转换器
        FlexmarkHtmlConverter converter = FlexmarkHtmlConverter.builder().build();
        // 直接转换
        String markdown = converter.convert(html);
        return markdown;
    }*/

    public static String convert(String html) {
        // 配置选项
        MutableDataSet options = new MutableDataSet()
//                .set(FlexmarkHtmlConverter.SUPPRESS_HTML, true)  // 是否压缩HTML
                .set(FlexmarkHtmlConverter.NBSP_TEXT, " ")      // 将&nbsp;转换为空格
                .set(FlexmarkHtmlConverter.BR_AS_EXTRA_BLANK_LINES, true);  // 将<br>转换为额外的空行
//                .set(FlexmarkHtmlConverter.CODE_CONTENT_BLOCK, true);      // 保留代码块格式

        // 创建转换器
        FlexmarkHtmlConverter converter = FlexmarkHtmlConverter.builder(options).build();

        // 转换
        String markdown = converter.convert(html);
        return markdown;
    }
}
