package com.jd.jdt.joycreator.ae;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.jdt.joycreator.ae.entity.PromptWordsConfig;
import com.jd.jdt.joycreator.ae.enums.ChatRoleEnum;
import com.jd.jdt.joycreator.ae.enums.PromptCodeEnum;
import com.jd.jdt.joycreator.ae.pojo.dto.*;
import com.jd.jdt.joycreator.ae.pojo.vo.AutofillVO;
import com.jd.jdt.joycreator.ae.pojo.vo.EditDocumentVO;
import com.jd.jdt.joycreator.ae.rpc.feign.LLMGatewayService;
import com.jd.jdt.joycreator.ae.service.EditDocumentService;
import com.jd.jdt.joycreator.ae.service.ChatStudioService;
import com.jd.jdt.joycreator.ae.service.PromptWordsConfigService;
import com.jd.jdt.joycreator.ae.utils.ContentAnchorAutoCompleteUtil;
import com.jd.jdt.joycreator.ae.utils.JoyCreatorUtil;
import com.jd.jdt.joycreator.ae.utils.JsonIntentListParserUtils;
import com.jd.jsf.gd.util.JsonUtils;
import feign.Response;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.*;


@Log4j2
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class LLMGatewayServiceTest {

    @Autowired
    private LLMGatewayService llmGatewayService;
    @Autowired
    private PromptWordsConfigService promptWordsConfigService;
    @Autowired
    private ChatStudioService editService;
    @Autowired
    private EditDocumentService editDocumentService;
    @Autowired
    private JoyCreatorUtil joyCreatorUtil;

    @Value("${rpc.llm.apiKey}")
    private String apiKey;
    @Value("${rpc.llm.jdcloudPin}")
    private String jdcloudPin;

    @Test
    public void videosResult2() {
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.AUTOFILL);
        List<DoronChatMessageDetailDTO> doronChatMessageDetailDTOList = Lists.newArrayList();
        PromptWordsConfigDTO promptWordsConfigDTO = new PromptWordsConfigDTO();
        BeanUtils.copyProperties(promptWordsConfig, promptWordsConfigDTO);
        DoronChatMessageDetailDTO doronChatMessageDetailDTO1 = new DoronChatMessageDetailDTO();
        doronChatMessageDetailDTO1.setChatRole(ChatRoleEnum.USER);
        doronChatMessageDetailDTO1.setChatContent("帮我写一份销售合同");
        doronChatMessageDetailDTOList.add(doronChatMessageDetailDTO1);
        DoronChatMessageDetailDTO doronChatMessageDetailDTO2 = new DoronChatMessageDetailDTO();
        doronChatMessageDetailDTO2.setChatRole(ChatRoleEnum.TASK_FEEDBACK);
        doronChatMessageDetailDTO2.setChatContent("我已经为您找到了一份相关的技术服务合同模版。");
        doronChatMessageDetailDTOList.add(doronChatMessageDetailDTO2);
        DoronChatMessageDetailDTO doronChatMessageDetailDTO3 = new DoronChatMessageDetailDTO();
        doronChatMessageDetailDTO3.setChatRole(ChatRoleEnum.USER);
        doronChatMessageDetailDTO3.setChatContent("帮我写一份技术服务协议,甲方为赵晗,乙方为张三,合同期限为2023年1月1日至2023年12月31日,标题替换为技术服务协议合同并润色一下合同双方");
        doronChatMessageDetailDTOList.add(doronChatMessageDetailDTO3);
        promptWordsConfigDTO.setDoronChatMessageDetailDTOList(doronChatMessageDetailDTOList);
        ChatCompletionResponseDTO chatCompletionResponseDTO = editService.chatCompletions(promptWordsConfigDTO);
        String content = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
        AutofillVO autofillVO = JsonIntentListParserUtils.parseObject(content, AutofillVO.class);
        log.info(JsonUtils.toJSONString(autofillVO));
        /*
        Map<String, Object> pa = Maps.newHashMap();
        pa.put("onKeyword", Boolean.FALSE);
        pa.put("prompts", "我是卖方立场，帮我找几份销售合同条款");
        try {
            Response response = llmGatewayService.videosResult2("sso.jd.com=BJ.1C897E5F9D209643FD7D2337CC6CF0D7.0720250408100313", pa);
            BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().asInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isEmpty(line)) {
                    continue;
                }
                System.err.println("Received event: " + line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }*/
    }

    @Test
    public void testEee2() {
        Map<String, Object> params = Maps.newHashMap();
        params.put("stream", Boolean.TRUE);
        params.put("model", "gpt-4o-0806");
        List messages = Lists.newArrayList();
        Map map = Maps.newHashMap();
//        map.put("role", "user");
        map.put("role", "system");
        map.put("content", "帮我写一份租赁合同");
        messages.add(map);
        params.put("messages", messages);
        try {
            Response response = llmGatewayService.chatCompletionsStream("aa051d32-db0b-4294-98a0-b02040240d48", params);
            BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().asInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isEmpty(line)) {
                    continue;
                } else if (line.contains("data: [DONE]")) {
                    log.info("Received event，任务结束：{}", line);
                    continue;
                } else if (line.startsWith("data: ")) {
                    line = line.substring(6);
                }
                ChatCompletionChunkDTO chatCompletionChunkDTO = JsonUtils.parseObject(line, ChatCompletionChunkDTO.class);
                log.info("Received event，chatCompletionChunkDTO：{}", JsonUtils.toJSONString(chatCompletionChunkDTO));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void testEee3() {
        Map<String, Object> params = Maps.newHashMap();
        params.put("model", "gpt-4o-0806");
        List messages = Lists.newArrayList();
        Map map = Maps.newHashMap();
//        map.put("role", "user");
        map.put("role", "user");
        map.put("content", "帮我写一份租赁合同");
        messages.add(map);
        params.put("messages", messages);
        log.info("Received event，params：{}", JsonUtils.toJSONString(params));
        ChatCompletionResponseDTO chatCompletionResponseDTO = llmGatewayService.chatCompletions("aa051d32-db0b-4294-98a0-b02040240d48", params);
        log.info("Received event，chatCompletionResponseDTO：{}", JsonUtils.toJSONString(chatCompletionResponseDTO));
    }


    @Test
    public void test5() {
        Map<String, Object> params = Maps.newHashMap();
//        params.put("model", "Chatrhino-81B-Pro");
        params.put("model", "text-embedding-ada-002-2");
        params.put("erp", "zhaohan25");
        params.put("input", "合同");
        log.info("Received event，params：{}", JsonUtils.toJSONString(params));
        String requestId = UUID.randomUUID().toString();
        EmbeddingResponseDTO embeddingResponseDTO = llmGatewayService.embeddings("aa051d32-db0b-4294-98a0-b02040240d48", requestId, params);
        System.err.println(JsonUtils.toJSONString(embeddingResponseDTO));
    }


    @Test
    public void test6() {
        EditDocumentVO editDocumentVO = editDocumentService.dtl("LS2025070200003");
        String bestAnchorContent = ContentAnchorAutoCompleteUtil.findBestAnchorContent(editDocumentVO.getTextContent(), "合同签署后【个工作日");
        System.err.println(bestAnchorContent);
//        String tuserSemantic = "合同签署后【】个工作日内填充为10个工作日";
//        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.DOCUMENT_REPLACE, tuserSemantic, editDocumentVO.getTextContent());
//        ChatCompletionResponseDTO chatCompletionResponseDTO = editService.chatCompletions(promptWordsConfig.getPromptWord(), promptWordsConfig.getModel());
//        String content = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
//        DocumentReplaceDTO documentReplaceDTO = joyCreatorUtil.parseDocumentReplaceJson(content);
//        if (Objects.isNull(documentReplaceDTO)) {
//            System.err.println("识别异常: " + content);
//        }
//        System.out.println(JsonUtils.toJSONString(documentReplaceDTO));
//        if (!editDocumentVO.getTextContent().contains(documentReplaceDTO.getOldContent())) {
//            String bestAnchorContent = ContentAnchorAutoCompleteUtil.findBestAnchorContent(editDocumentVO.getTextContent(), documentReplaceDTO.getOldContent());
//            System.err.println("识别错误: " + documentReplaceDTO.getOldContent());
//            System.err.println("识别错误: " + s);
//            if (editDocumentVO.getTextContent().contains(bestAnchorContent)) {
//                System.out.println("识别错误,但是纠正过来了: " + bestAnchorContent);
//            }else {
//                System.err.println("识别错误,没有纠正过来: " + bestAnchorContent);
//            }
//        }else {
//            System.out.println("识别正确: " + JsonUtils.toJSONString(documentReplaceDTO));
//        }
    }


    @Test
    public void test7() {
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.CHAT_INTENT_RECOGNITION);
        Map<String, Object> params = new HashMap<>();
        params.put("model", promptWordsConfig.getModel());
        List<Object> messages = Lists.newArrayList();
        Map<String, String> map = Maps.newHashMap();
        map.put("role", "system");
        map.put("content", promptWordsConfig.getPromptWord());
        Map<String, String> map2 = Maps.newHashMap();
        map2.put("role", "user");
        map2.put("content", "你好");
        Map<String, String> map3 = Maps.newHashMap();
        map3.put("role", "user");
        map3.put("content", "帮我写一份合同");
        Map<String, String> map4 = Maps.newHashMap();
        map4.put("role", "user");
        map4.put("content", "销售");
        Map<String, String> map5 = Maps.newHashMap();
        map5.put("role", "user");
        map5.put("content", "帮我写一份租赁合同,标题替换为公司考勤制度管理，联系人改为张三，并且改写一下1.1商机管理部分内容，使内容更加丰富，同时帮我润色一下2.1客户管理部分的内容");
        Map<String, String> map6 = Maps.newHashMap();
        map6.put("role", "user");
        map6.put("content", "我刚才让你帮我做过什么事情?");
        Map<String, String> map7 = Maps.newHashMap();
        map7.put("role", "user");
        map7.put("content", "今天是多少号?");
        messages.add(map);
        messages.add(map2);
        messages.add(map3);
        messages.add(map4);
//        messages.add(map5);
//        messages.add(map6);
//        messages.add(map7);
        params.put("messages", messages);
        if (Objects.nonNull(promptWordsConfig.getMaxTokens())) {
            params.put("max_tokens", promptWordsConfig.getMaxTokens().longValue());
        }
        if (Objects.nonNull(promptWordsConfig.getTemperature()) || Objects.nonNull(promptWordsConfig.getTopK())) {
            params.put("do_sample", Boolean.TRUE);
            if (Objects.nonNull(promptWordsConfig.getTemperature())) {
                params.put("temperature", promptWordsConfig.getTemperature().floatValue());
            }
            if (Objects.nonNull(promptWordsConfig.getTopK())) {
                params.put("top_p", promptWordsConfig.getTopK().floatValue());
            }
        }
//        log.info("聊天完成接口/非流式，apiKey：{}，params：{}", apiKey, JsonUtils.toJSONString(params));
        ChatCompletionResponseDTO chatCompletionResponseDTO = llmGatewayService.chatCompletions(apiKey, params);
        String content = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
        log.info("模型回复:{}", content);
    }


    public void test8(){
        PromptWordsConfig promptWordsConfig = promptWordsConfigService.buildPromptWordsConfig(PromptCodeEnum.AUTOFILL);
        List<DoronChatMessageDetailDTO> doronChatMessageDetailDTOList = Lists.newArrayList();
        PromptWordsConfigDTO promptWordsConfigDTO = new PromptWordsConfigDTO();
        BeanUtils.copyProperties(promptWordsConfig, promptWordsConfigDTO);
        DoronChatMessageDetailDTO doronChatMessageDetailDTO1 = new DoronChatMessageDetailDTO();
        doronChatMessageDetailDTO1.setChatRole(ChatRoleEnum.USER);
        doronChatMessageDetailDTO1.setChatContent("帮我写一份销售合同");
        doronChatMessageDetailDTOList.add(doronChatMessageDetailDTO1);
        DoronChatMessageDetailDTO doronChatMessageDetailDTO2 = new DoronChatMessageDetailDTO();
        doronChatMessageDetailDTO2.setChatRole(ChatRoleEnum.TASK_FEEDBACK);
        doronChatMessageDetailDTO2.setChatContent("我已经为您找到了一份相关的技术服务合同模版。");
        doronChatMessageDetailDTOList.add(doronChatMessageDetailDTO2);
        DoronChatMessageDetailDTO doronChatMessageDetailDTO3 = new DoronChatMessageDetailDTO();
        doronChatMessageDetailDTO3.setChatRole(ChatRoleEnum.USER);
        doronChatMessageDetailDTO3.setChatContent("换一批模版");
        doronChatMessageDetailDTOList.add(doronChatMessageDetailDTO3);
        promptWordsConfigDTO.setDoronChatMessageDetailDTOList(doronChatMessageDetailDTOList);
        ChatCompletionResponseDTO chatCompletionResponseDTO = editService.chatCompletions(promptWordsConfig);
        String content = chatCompletionResponseDTO.getChoices().get(0).getMessage().getContent();
        AutofillVO autofillVO = JsonIntentListParserUtils.parseObject(content, AutofillVO.class);
        log.info(JsonUtils.toJSONString(autofillVO));
    }
}