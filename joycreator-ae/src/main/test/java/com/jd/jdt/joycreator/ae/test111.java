package com.jd.jdt.joycreator.ae;

import com.aspose.words.Document;
import com.aspose.words.SaveFormat;
import com.jd.jdt.joycreator.ae.enums.ToolLabelsEnum;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class test111 {

    public static void main(String[] args) throws Exception {
        String src = "/Users/<USER>/IdeaProjects/JoyCreator2/joycreator-ae/src/main/resources/scripts/【样例】技术服务协议模板（jd为销售方通用版）.docx";
        String dst = "/Users/<USER>/IdeaProjects/JoyCreator2/joycreator-ae/src/main/resources/scripts/【样例】技术服务协议模板（jd为销售方通用版）.pdf";

        File file = new File(dst);
        FileOutputStream os = new FileOutputStream(file);
        Document doc = new Document(src);
        // 使用PDF格式进行测试，这是最稳定的格式
        doc.save(os, SaveFormat.PDF);

        // 测试用例1: template_library工具
        String xmlContent1 = "<template_library>\n" +
                "  <template_name>技术服务合同</template_name>\n" +
                "  <template_type>合同</template_type>\n" +
                "</template_library>";
        
        System.out.println("=== 通用XML工具解析测试 ===");
        
        // 先测试一个简单的例子
        System.out.println("测试XML内容:");
        System.out.println(xmlContent1);
        
        // 测试参数解析
        Map<String, Object> params = parseAllXmlParams(xmlContent1);
        System.out.println("解析得到的参数: " + params);
        
        // 测试完整解析
        testXmlParsing("template_library", xmlContent1);
    }
    
    /**
     * 测试XML解析功能
     */
    private static void testXmlParsing(String toolName, String xmlContent) {
        System.out.println("\n--- 测试 " + toolName + " 工具 ---");
        System.out.println("输入XML:");
        System.out.println(xmlContent);
        
        // 1. 识别最外层XML标签
        ToolLabelsEnum toolLabel = identifyToolLabel(xmlContent);
        System.out.println("工具标签: " + toolLabel);
        
        // 2. 通用解析XML标签内的所有参数
        Map<String, Object> toolParams = parseAllXmlParams(xmlContent);
        System.out.println("工具参数: " + toolParams);
        
        System.out.println("----------------------------------------");
    }
    
    /**
     * 识别工具标签 - 通用方法，支持所有在ToolLabelsEnum中定义的工具
     * @param xmlContent XML内容
     * @return ToolLabelsEnum
     */
    private static ToolLabelsEnum identifyToolLabel(String xmlContent) {
        // 匹配最外层XML标签的正则表达式
        Pattern pattern = Pattern.compile("<([^/>\\s]+)[^>]*>");
        Matcher matcher = pattern.matcher(xmlContent.trim());
        
        if (matcher.find()) {
            String tagName = matcher.group(1);
            String fullTag = "<" + tagName + ">";
            
            // 遍历枚举，找到匹配的工具标签
            for (ToolLabelsEnum toolEnum : ToolLabelsEnum.values()) {
                if (toolEnum.getType().equals(fullTag)) {
                    return toolEnum;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 通用XML参数解析方法 - 自动解析XML中的所有子标签
     * @param xmlContent XML内容
     * @return 参数Map，key为标签名，value为标签值
     */
    private static Map<String, Object> parseAllXmlParams(String xmlContent) {
        Map<String, Object> params = new HashMap<>();
        
        System.out.println("调试 - 开始解析XML参数");
        System.out.println("调试 - 输入内容: " + xmlContent);
        
        // 先提取最外层标签内的内容
        Pattern outerPattern = Pattern.compile("<[^>]+>(.*)</[^>]+>", Pattern.DOTALL);
        Matcher outerMatcher = outerPattern.matcher(xmlContent.trim());
        
        String innerContent = "";
        if (outerMatcher.find()) {
            innerContent = outerMatcher.group(1);
            System.out.println("调试 - 提取的内部内容: " + innerContent);
        } else {
            System.out.println("调试 - 无法提取内部内容");
            return params;
        }
        
        // 然后解析内部的所有标签
        Pattern pattern = Pattern.compile("<([^/>\\s]+)\\s*>(.*?)</\\1>", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(innerContent);
        
        while (matcher.find()) {
            String tagName = matcher.group(1).trim();
            String tagValue = matcher.group(2).trim();
            System.out.println("调试 - 找到参数: " + tagName + " = " + tagValue);
            params.put(tagName, tagValue);
        }
        
        System.out.println("调试 - 最终参数: " + params);
        return params;
    }
}
