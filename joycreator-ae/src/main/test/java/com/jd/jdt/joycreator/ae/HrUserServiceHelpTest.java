package com.jd.jdt.joycreator.ae;

import com.jd.jdt.joycreator.ae.dao.plus.ITemplateLibraryMapper;
import com.jd.jdt.joycreator.ae.entity.TemplateLibrary;
import com.jd.jdt.joycreator.ae.pojo.dto.*;
import com.jd.jdt.joycreator.ae.rpc.jsf.HrUserServiceHelp;
import com.jd.jsf.gd.util.JsonUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

@Log4j2
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class HrUserServiceHelpTest {

    @Autowired
    private HrUserServiceHelp hrUserServiceHelp;
    @Autowired
    private ITemplateLibraryMapper iTemplateLibraryMapper;

    @Test
    public void test() {
        HrResultDTO<HrUserBasicDTO> zhaohan = hrUserServiceHelp.queryUserByErp("zhaohan");
        log.info(JsonUtils.toJSONString(zhaohan));

    }

    @Test
    public void test2() {
        try {
            // 使用ClassPathResource读取response.html文件内容
            ClassPathResource resource = new ClassPathResource("scripts/【样例】技术服务协议模板（jd为销售方通用版）.html");
            InputStream inputStream = resource.getInputStream();
            String content = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            inputStream.close();
            TemplateLibrary templateLibrary = new TemplateLibrary();
//            templateLibrary.setId(953780042903498760L);
            templateLibrary.setTag("技术合同");
            templateLibrary.setName("【样例】技术服务协议模板（jd为销售方通用版）.docx");
            templateLibrary.setDescription("这是一份技术合同文档模版");
            templateLibrary.setContent(content);
            boolean save = iTemplateLibraryMapper.saveOrUpdate(templateLibrary);
            log.info("保存结果: " + save);
        } catch (IOException e) {
            log.error("读取文件失败", e);
        }
    }
}
