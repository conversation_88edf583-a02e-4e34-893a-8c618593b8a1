<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.jd.jdt.joycreator</groupId>
        <artifactId>joycreator</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>joycreator-ae</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.jd.jdt.app4s.component</groupId>
            <artifactId>app-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd</groupId>
                    <artifactId>jsf-lite</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.jdt.joybuilder</groupId>
            <artifactId>joybuilder-adapter-core-export</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.jd</groupId>
                    <artifactId>jsf-lite</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.jdt.joybuilder</groupId>
            <artifactId>joybuilder-permission</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.jd.jdt.app4s.component</groupId>
                    <artifactId>app-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd</groupId>
                    <artifactId>jsf-lite</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.jdt.joycreator</groupId>
            <artifactId>joycreator-restapi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jdt.joycreator</groupId>
            <artifactId>joycreator-trigger</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-jul</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.apache.logging.log4j</groupId>-->
        <!--            <artifactId>log4j-slf4j-impl</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jul</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <!-- mybatis -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-support</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>javax.persistence-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
        </dependency>
        <!--S3 oss存储-->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
        </dependency>
        <dependency>
            <groupId>com.jd.autobots</groupId>
            <artifactId>autobots-client</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
        </dependency>
        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.vertical-blank</groupId>
            <artifactId>sql-formatter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdt.toolgood</groupId>
            <artifactId>toolgood-words</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>2.14.5</version>
        </dependency>
        <dependency>
            <groupId>com.jd.jdt.tob</groupId>
            <artifactId>tob-word</artifactId>
        </dependency>
        <dependency>
            <groupId>technology.tabula</groupId>
            <artifactId>tabula</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.java-diff-utils</groupId>
            <artifactId>java-diff-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jdt.tob</groupId>
            <artifactId>tob-diff</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jdt.tob</groupId>
            <artifactId>tob-pdf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hynnet</groupId>
            <artifactId>jacob</artifactId>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.jd.jdt.app4s</groupId>
            <artifactId>app4s-permission-embed</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!-- CCO模型安全网关 -->
        <dependency>
            <groupId>com.jd.security.llmsec</groupId>
            <artifactId>llm-sec-core</artifactId>
            <!--      建议使用最新版本jar，记得去掉SNAPSHOT-->
            <version>0.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.jd.jdq</groupId>
            <artifactId>jdq4-clients</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.jd.jdr.aiporta</groupId>
            <artifactId>ai-business-export</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsf</artifactId>
                    <groupId>com.jd</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 对应2.3.3-RC2版本客户端 -->
        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq2-client-springboot-starter</artifactId>
            <version>*******</version>
        </dependency>
        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq-client-core</artifactId>
            <version>2.3.3-RC2</version>
        </dependency>
        <dependency>
            <artifactId>star-message-api</artifactId>
            <groupId>com.jd.jdt.star</groupId>
        </dependency>
        <dependency>
            <groupId>com.jd.jdt.risk</groupId>
            <artifactId>risk-export-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jr.qyjr</groupId>
            <artifactId>customerapi-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.fms.clearing.checkrisk</groupId>
            <artifactId>checkrisk-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.autobots</groupId>
            <artifactId>autobots-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jr</groupId>
            <artifactId>jr-csp-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jddglobal.ecrm</groupId>
            <artifactId>ecrm-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.icc</groupId>
            <artifactId>icc-data-query-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jdt.tob</groupId>
            <artifactId>tob-sp-doc</artifactId>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.vladsch.flexmark</groupId>
            <artifactId>flexmark-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.vladsch.flexmark</groupId>
            <artifactId>flexmark-html2md-converter</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.4.5</version>
                <configuration>
                    <mainClass>com.jd.jdt.joycreator.ae.Application</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.7</version>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.shared</groupId>
                        <artifactId>maven-filtering</artifactId>
                        <version>1.3</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <delimiters>
                        <delimiter>@</delimiter>
                    </delimiters>
                    <useDefaultDelimiters>false</useDefaultDelimiters>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>TTF</nonFilteredFileExtension>
                        <nonFilteredFileExtension>png</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jpg</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <!--引入外部的jar包的plugin-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.targetEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <descriptors>
                        <descriptor>assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <!-- attached to Maven test phase -->
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- 本地环境 -->
        <profile>
            <id>local</id>
            <properties>
                <spring.profiles.active>local</spring.profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!-- 预发环境 -->
        <profile>
            <id>pre</id>
            <properties>
                <spring.profiles.active>pre</spring.profiles.active>
            </properties>
        </profile>
        <!-- 平台开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
        </profile>
        <!-- 测试环境 -->
        <profile>
            <id>test</id>
            <properties>
                <spring.profiles.active>test</spring.profiles.active>
            </properties>
        </profile>
        <!-- 生产环境 -->
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
    </profiles>
</project>
