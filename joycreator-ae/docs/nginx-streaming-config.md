# Nginx 流式响应优化配置建议

## 问题背景
在生产环境中，Nginx 等反向代理服务器可能会启用响应缓冲，这会导致流式响应被缓冲而无法实时传输给客户端。

## 解决方案

### 1. 全局配置优化
在 `nginx.conf` 的 `http` 块中添加以下配置：

```nginx
http {
    # 禁用代理缓冲（全局设置）
    proxy_buffering off;
    proxy_cache off;
    
    # 设置代理超时
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 300s;  # 流式响应可能需要更长时间
    
    # 禁用 gzip 压缩（对流式响应）
    gzip off;
    
    # 其他配置...
}
```

### 2. 针对流式接口的特定配置
在对应的 `server` 或 `location` 块中：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 针对流式聊天接口的特殊配置
    location /api/v1/llm/chat/ {
        # 禁用缓冲
        proxy_buffering off;
        proxy_cache off;
        
        # 支持 HTTP/1.1 和 Keep-Alive
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        
        # 传递原始请求头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 关键：支持应用程序的 X-Accel-Buffering 头
        proxy_set_header X-Accel-Buffering $http_x_accel_buffering;
        
        # 代理到后端服务
        proxy_pass http://backend-servers;
        
        # 流式响应专用超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;  # 允许长时间的流式响应
    }
    
    # 其他接口的正常配置
    location / {
        proxy_pass http://backend-servers;
        # 正常的缓冲配置...
    }
}
```

### 3. 上游服务器配置
```nginx
upstream backend-servers {
    server 127.0.0.1:8080 max_fails=3 fail_timeout=30s;
    # 可以添加更多后端服务器
    keepalive 32;  # 保持连接池
}
```

### 4. 验证配置
部署后可以通过以下方式验证：

1. **检查响应头**：
   ```bash
   curl -I http://your-domain.com/api/v1/llm/chat/doron
   ```
   应该看到：
   - `Transfer-Encoding: chunked`
   - `Cache-Control: no-cache`
   - `X-Accel-Buffering: no`

2. **测试流式响应**：
   ```bash
   curl -X POST http://your-domain.com/api/v1/llm/chat/doron \
     -H "Content-Type: application/json" \
     -d '{"sessionId":"test","stream":true,"messages":[...]}' \
     --no-buffer
   ```

### 5. 监控和调试
在 Nginx 日志中添加相关变量：

```nginx
log_format streaming '$remote_addr - $remote_user [$time_local] '
                    '"$request" $status $body_bytes_sent '
                    '"$http_referer" "$http_user_agent" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time" '
                    'buffering=$http_x_accel_buffering';

access_log /var/log/nginx/streaming.log streaming;
```

## 注意事项

1. **性能影响**：禁用缓冲可能会增加 Nginx 的内存使用和 CPU 负载
2. **网络稳定性**：确保客户端网络稳定，避免连接中断
3. **超时设置**：根据实际业务需求调整超时时间
4. **负载均衡**：在多实例部署时，考虑会话粘性

## 测试建议

1. 在测试环境先验证配置
2. 使用压力测试工具验证并发性能
3. 监控服务器资源使用情况
4. 测试各种网络条件下的表现

## 故障排查

如果流式响应仍然被缓冲：

1. 检查 `X-Accel-Buffering: no` 头是否正确传递
2. 确认 Nginx 版本支持相关指令
3. 检查是否有其他中间件（如 CDN）启用了缓冲
4. 验证应用程序是否正确设置了响应头